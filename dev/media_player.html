<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Race Tracking Media Player</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/hls.js@latest"></script>
    <style>
        body {
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        .container {
            max-width: 900px;
        }
        #videoPlayer {
            width: 100%;
            background-color: #000;
        }
        .controls {
            margin: 20px 0;
        }
        h1 {
            margin-bottom: 20px;
        }
        .timestamp-info {
            margin-top: 10px;
            font-size: 0.9rem;
            color: #666;
        }
        .badge {
            margin-left: 5px;
        }
        .badge-camera {
            background-color: #e74c3c;
        }
        .badge-start {
            background-color: #27ae60;
        }
        .filter-controls {
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Race Tracking Media Player</h1>
        
        <div class="filter-controls">
            <div class="form-check form-check-inline">
                <input class="form-check-input" type="checkbox" id="showCamera" checked>
                <label class="form-check-label" for="showCamera">
                    <span class="badge bg-danger">Camera</span> Camera Events
                </label>
            </div>
            <div class="form-check form-check-inline">
                <input class="form-check-input" type="checkbox" id="showStartLine" checked>
                <label class="form-check-label" for="showStartLine">
                    <span class="badge bg-success">Start</span> Start Line Events
                </label>
            </div>
        </div>
        
        <div class="controls">
            <div class="row">
                <div class="col-md-8">
                    <select id="timestampSelect" class="form-select">
                        <option value="">Select a timestamp...</option>
                        <!-- Options will be populated by JavaScript -->
                    </select>
                    <div class="timestamp-info" id="timestampInfo"></div>
                </div>
                <div class="col-md-4">
                    <button id="playButton" class="btn btn-primary w-100" disabled>Play Video</button>
                </div>
            </div>
        </div>
        
        <video id="videoPlayer" controls></video>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Elements
            const timestampSelect = document.getElementById('timestampSelect');
            const playButton = document.getElementById('playButton');
            const videoPlayer = document.getElementById('videoPlayer');
            const timestampInfo = document.getElementById('timestampInfo');
            const showCamera = document.getElementById('showCamera');
            const showStartLine = document.getElementById('showStartLine');

            // Base URL for the video stream
            const baseUrl = 'http://**************:8081/srt/9/playlist_dvr_range-';
            const videoLength = 728; // Video length in seconds

            // Store all events
            let allEvents = [];

            // Function to adjust UTC timestamp for the streaming server (add 2 hours and subtract 10 seconds)
            function adjustTimestampForServer(utcTimestamp) {
                // Add 2 hours (7200 seconds) to match SAST timezone, then subtract 10 seconds for context
                return parseInt(utcTimestamp) + 7200 - 10;
            }

            // Function to get location type badge
            function getLocationBadge(locationType) {
                if (locationType === 'start_line') {
                    return '<span class="badge bg-success">Start</span>';
                } else {
                    return '<span class="badge bg-danger">Camera</span>';
                }
            }

            // Function to update dropdown options based on filters
            function updateDropdownOptions() {
                // Clear current options
                while (timestampSelect.options.length > 1) {
                    timestampSelect.remove(1);
                }

                // Filter events based on checkboxes
                const filteredEvents = allEvents.filter(event => {
                    if (event.locationType === 'start_line' && !showStartLine.checked) {
                        return false;
                    }
                    if (event.locationType !== 'start_line' && !showCamera.checked) {
                        return false;
                    }
                    return true;
                });

                // Populate dropdown with filtered events
                filteredEvents.forEach(event => {
                    const option = document.createElement('option');
                    option.value = event.utcTimestamp;

                    // Format time for display
                    const localTime = new Date(event.time).toLocaleTimeString();
                    const badge = getLocationBadge(event.locationType);
                    option.innerHTML = `${localTime} - ${event.participantId} (${event.driverNames}) ${badge}`;

                    // Store full event data
                    option.dataset.event = JSON.stringify(event);

                    timestampSelect.appendChild(option);
                });

                // Enable play button if options are available
                if (filteredEvents.length > 0) {
                    playButton.disabled = false;
                } else {
                    playButton.disabled = true;
                    timestampInfo.textContent = 'No events found with current filters';
                }
            }

            // Function to generate playlist for a specific competitor
            async function generatePlaylist(competitorId) {
                let trackingData = [];
                try {
                    const { default: extractTrackingData } = await import('./extract_tracking_data.js');
                    trackingData = await extractTrackingData(competitorId);
                } catch (error) {
                    console.error("Error importing extract_tracking_data.js:", error);
                    timestampInfo.textContent = 'Error loading tracking data';
                    playButton.disabled = true;
                    return;
                }

                // Clear current options
                while (timestampSelect.options.length > 1) {
                    timestampSelect.remove(1);
                }

                // Populate dropdown with playlist items
                if (trackingData) {
                    trackingData.forEach(item => {
                        const option = document.createElement('option');
                        // Extract timestamp from the item
                        // Convert SAST timestamp to UTC
                        const sastTime = new Date(item.timestamp);
                        const utcTime = new Date(sastTime.getTime() - sastTime.getTimezoneOffset() * 60000);
                        const timestamp = utcTime.getTime() / 1000; // Convert to seconds

                        option.value = timestamp;

                        // Format time for display
                        const localTime = new Date(timestamp * 1000).toLocaleTimeString();
                        option.innerHTML = `${localTime} - Competitor ${competitorId}`;

                        timestampSelect.appendChild(option);
                    });

                    // Enable play button if options are available
                    playButton.disabled = trackingData.length === 0;
                    timestampInfo.textContent = trackingData.length > 0
                        ? `Playlist generated for competitor ${competitorId}`
                        : `No tracking data found for competitor ${competitorId}`;
                } else {
                    playButton.disabled = true;
                    timestampInfo.textContent = `No tracking data found for competitor ${competitorId}`;
                }
            }

            // Load camera proximity events from JSON
            Promise.all([
                    fetch('camera_proximity_events.json').then(response => response.json()),
                    fetch('start_line_proximity_events.json').then(response => response.json())
                ])
                .then(([cameraEvents, startLineEvents]) => {
                    // Add location type to camera events if not already present
                    cameraEvents.forEach(event => {
                        if (!event.locationType) {
                            event.locationType = 'camera';
                        }
                    });

                    // Combine all events
                    allEvents = [...cameraEvents, ...startLineEvents];

                    // Filter events before 14:00 (UTC timestamp 1742046000 = 14:00 SAST on the day)
                    const cutoffTime = 1742046000; // 14:00 SAST
                    allEvents = allEvents.filter(event => event.utcTimestamp < cutoffTime);

                    // Sort by time
                    allEvents.sort((a, b) => a.utcTimestamp - b.utcTimestamp);

                    // Update dropdown options
                    updateDropdownOptions();
                })
                .catch(error => {
                    console.error('Error loading data:', error);
                    timestampInfo.textContent = 'Error loading event data';
                });

            // Handle timestamp selection
            timestampSelect.addEventListener('change', function() {
                if (this.value) {
                    timestampInfo.textContent = `Selected timestamp: ${new Date(this.value * 1000).toLocaleTimeString()}`;
                    playButton.disabled = false;
                } else {
                    timestampInfo.textContent = '';
                    playButton.disabled = true;
                }
            });

            // Handle filter changes
            showCamera.addEventListener('change', updateDropdownOptions);
            showStartLine.addEventListener('change', updateDropdownOptions);

            // Play button handler
            playButton.addEventListener('click', function() {
                const timestamp = timestampSelect.value;
                if (!timestamp) return;

                // Update info text
                timestampInfo.textContent = 'Loading video...';

                // Adjust timestamp for server (add 2 hours)
                const adjustedTimestamp = adjustTimestampForServer(timestamp);

                // Construct the video URL with adjusted timestamp
                const videoUrl = `${baseUrl}${adjustedTimestamp}-${videoLength}.m3u8`;
                console.log('Attempting to play:', videoUrl);
                console.log('Original timestamp:', timestamp, 'Adjusted timestamp:', adjustedTimestamp);

                // Check if the playlist is valid before playing
                fetch(videoUrl)
                    .then(response => response.text())
                    .then(data => {
                        if (data.includes('#EXT-X-ENDLIST') && !data.includes('EXTINF')) {
                            // Empty playlist detected
                            throw new Error('Empty playlist - No video data available for this timestamp');
                        }

                        // Play the video using HLS.js if supported
                        if (Hls.isSupported()) {
                            const hls = new Hls({
                                debug: true,
                                enableWorker: true
                            });

                            hls.on(Hls.Events.ERROR, function(event, data) {
                                console.error('HLS Error:', data);
                                if (data.fatal) {
                                    timestampInfo.textContent = `Playback error: ${data.details}`;
                                    hls.destroy();
                                }
                            });

                            hls.loadSource(videoUrl);
                            hls.attachMedia(videoPlayer);
                            hls.on(Hls.Events.MANIFEST_PARSED, function() {
                                videoPlayer.play()
                                    .then(() => {
                                        timestampInfo.textContent = `Playing video at ${new Date(timestamp * 1000).toLocaleTimeString()}`;
                                    })
                                    .catch(err => {
                                        console.error('Play error:', err);
                                        timestampInfo.textContent = 'Playback failed - ' + err.message;
                                    });
                            });
                        }
                        // For Safari which has native HLS support
                        else if (videoPlayer.canPlayType('application/vnd.apple.mpegurl')) {
                            videoPlayer.src = videoUrl;
                            videoPlayer.addEventListener('loadedmetadata', function() {
                                videoPlayer.play()
                                    .then(() => {
                                        timestampInfo.textContent = `Playing video at ${new Date(timestamp * 1000).toLocaleTimeString()}`;
                                    })
                                    .catch(err => {
                                        timestampInfo.textContent = 'Playback failed - ' + err.message;
                                    });
                            });

                            videoPlayer.addEventListener('error', function() {
                                timestampInfo.textContent = 'Video playback error: ' + (videoPlayer.error ? videoPlayer.error.message : 'Unknown error');
                            });
                        } else {
                            timestampInfo.textContent = 'HLS playback not supported in this browser';
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        timestampInfo.textContent = error.message || 'Failed to load video';
                    });
            });

            // Add error handler for video element
            videoPlayer.addEventListener('error', function() {
                console.error('Video error:', videoPlayer.error);
                timestampInfo.textContent = 'Video error: ' + (videoPlayer.error ? videoPlayer.error.message : 'Unknown error');
            });

            // Generate playlist on load
            generatePlaylist('777');
        });
    </script>
</body>
</html>
