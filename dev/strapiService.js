const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');
const { strapiUrl, strapiApiToken, maxRetries } = require('../utils/config');
const logger = require('../utils/logger');

/**
 * Upload a file to Strapi Media Library
 * @param {string} filePath - Path to the file to upload
 * @param {number} retryCount - Current retry count
 * @returns {Object} Uploaded file data
 */
async function uploadFileToStrapi(filePath, retryCount = 0) {
  try {
    const form = new FormData();
    form.append('files', fs.createReadStream(filePath));
    
    const response = await axios.post(`${strapiUrl}/api/upload`, form, {
      headers: {
        ...form.getHeaders(),
        Authorization: `Bearer ${strapiApiToken}`,
      },
    });
    
    logger.debug(`Uploaded file to Strapi: ${filePath}`);
    logger.debug('Strapi upload response:', JSON.stringify(response.data, null, 2));
    
    const fileData = response.data[0]; // Strapi returns an array of uploaded files
    
    // Enhance the file data to make sure we have the necessary information
    // This ensures we have the correct ID format regardless of Strapi version
    if (fileData) {
      // Make sure we have an id field
      if (!fileData.id && fileData._id) {
        fileData.id = fileData._id;
      }
      
      // Make sure we have a url field
      if (!fileData.url && fileData.formats && fileData.formats.thumbnail) {
        const baseUrl = fileData.formats.thumbnail.url.split('/uploads/')[0];
        fileData.url = `${baseUrl}/uploads/${fileData.hash}${fileData.ext}`;
      }
      
      // Ensure we have a proper formats structure if missing
      if (!fileData.formats) {
        fileData.formats = {
          thumbnail: { url: fileData.url }
        };
      }
    }
    
    return fileData;
  } catch (error) {
    logger.error(`Failed to upload file to Strapi: ${filePath}`, error);
    
    if (retryCount < maxRetries) {
      logger.info(`Retrying upload for ${filePath} (Attempt ${retryCount + 1}/${maxRetries})`);
      // Exponential backoff
      await new Promise(resolve => setTimeout(resolve, 1000 * Math.pow(2, retryCount)));
      return uploadFileToStrapi(filePath, retryCount + 1);
    }
    
    throw error;
  }
}

/**
 * Create a TelegramContent entry in Strapi using GraphQL
 * @param {Array} uploadedFiles - Array of uploaded file data from Strapi
 * @param {Object} metadata - Metadata for the upload
 * @returns {Object} Created TelegramContent entry
 */
async function createTelegramContentWithGraphQL(uploadedFiles, metadata) {
  try {
    // Generate a simple random document ID (similar to the sample data)
    const documentId = [...Array(24)].map(() => 
      "abcdefghijklmnopqrstuvwxyz0123456789"[Math.floor(Math.random() * 36)]
    ).join('');
    
    // For debugging purposes, log the uploaded files structure
    logger.debug('Uploaded files before creating content:', JSON.stringify(uploadedFiles, null, 2));
    
    // Create GraphQL mutation for Strapi 5
    // Modified based on error feedback - using data/attributes pattern
    const graphqlQuery = {
      query: `
        mutation CreateTelegramContent($input: TelegramContentInput!) {
          createTelegramContent(data: $input) {
            data {
              attributes {
                documentId
                username
                timestamp
                media_type
                source
                latitude
                longitude
                original_filename
                createdAt
                updatedAt
                publishedAt
                media {
                  data {
                    attributes {
                      url
                    }
                  }
                }
              }
            }
          }
        }
      `,
      variables: {
        input: {
          documentId,
          username: metadata.username,
          timestamp: new Date().toISOString(),
          media_type: metadata.mediaType,
          source: 'telegram',
          latitude: metadata.latitude,
          longitude: metadata.longitude,
          original_filename: metadata.originalFilename,
          // Connect media files for Strapi 5
          media: uploadedFiles.map(file => file.id)
        }
      }
    };
    
    // Log the GraphQL query for debugging
    logger.debug('GraphQL mutation:', JSON.stringify(graphqlQuery, null, 2));
    
    const response = await axios.post(
      `${strapiUrl}/graphql`, 
      graphqlQuery, 
      {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${strapiApiToken}`,
        },
      }
    );
    
    if (response.data.errors) {
      logger.error('GraphQL errors:', JSON.stringify(response.data.errors, null, 2));
      throw new Error(JSON.stringify(response.data.errors));
    }
    
    logger.debug(`Created TelegramContent entry in Strapi`, JSON.stringify(response.data, null, 2));
    return response.data.data?.createTelegramContent || response.data;
  } catch (error) {
    // Log more details about the error
    logger.error('Failed to create TelegramContent entry with GraphQL', error);
    if (error.response) {
      logger.error('Error response data:', JSON.stringify(error.response.data, null, 2));
    }
    throw error;
  }
}

/**
 * Create a TelegramContent entry in Strapi using REST API
 * @param {Array} uploadedFiles - Array of uploaded file data from Strapi
 * @param {Object} metadata - Metadata for the upload
 * @returns {Object} Created TelegramContent entry
 */
async function createTelegramContentWithREST(uploadedFiles, metadata) {
  try {
    // Generate a simple random document ID (similar to the sample data)
    const documentId = [...Array(24)].map(() => 
      "abcdefghijklmnopqrstuvwxyz0123456789"[Math.floor(Math.random() * 36)]
    ).join('');
    
    // Prepare payload for Strapi 5 format - based on your sample data
    const payload = {
      documentId,
      username: metadata.username,
      timestamp: new Date().toISOString(),
      media_type: metadata.mediaType,
      source: 'telegram',
      latitude: metadata.latitude,
      longitude: metadata.longitude,
      original_filename: metadata.originalFilename,
      // For media, include the IDs
      media: uploadedFiles.map(file => file.id)
    };
    
    logger.debug('REST API payload for Strapi 5:', JSON.stringify(payload, null, 2));
    
    const response = await axios.post(
      `${strapiUrl}/api/telegram-contents`, 
      payload, 
      {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${strapiApiToken}`,
        },
      }
    );
    
    logger.debug(`Created TelegramContent entry in Strapi with REST API`, response.data);
    return response.data;
  } catch (error) {
    logger.error('Failed to create TelegramContent entry with REST API', error);
    if (error.response) {
      logger.error('Error response data:', JSON.stringify(error.response.data, null, 2));
    }
    
    // Try with 'data' wrapper which some Strapi 5 instances require
    try {
      logger.info('Trying with data wrapper format');
      
      // Generate a simple random document ID
      const documentId = [...Array(24)].map(() => 
        "abcdefghijklmnopqrstuvwxyz0123456789"[Math.floor(Math.random() * 36)]
      ).join('');
      
      const wrappedPayload = {
        data: {
          documentId,
          username: metadata.username,
          timestamp: new Date().toISOString(),
          media_type: metadata.mediaType,
          source: 'telegram',
          latitude: metadata.latitude,
          longitude: metadata.longitude,
          original_filename: metadata.originalFilename,
          media: uploadedFiles.map(file => file.id)
        }
      };
      
      logger.debug('REST API payload with data wrapper:', JSON.stringify(wrappedPayload, null, 2));
      
      const wrappedResponse = await axios.post(
        `${strapiUrl}/api/telegram-contents`, 
        wrappedPayload, 
        {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${strapiApiToken}`,
          },
        }
      );
      
      logger.debug(`Created TelegramContent entry with data wrapper`, wrappedResponse.data);
      return wrappedResponse.data;
    } catch (wrappedError) {
      logger.error('Failed with data wrapper format too', wrappedError);
      throw error; // Throw the original error
    }
  }
}

/**
 * Create a TelegramContent entry in Strapi (skips GraphQL and uses REST directly)
 * @param {Array} uploadedFiles - Array of uploaded file data from Strapi
 * @param {Object} metadata - Metadata for the upload
 * @returns {Object} Created TelegramContent entry
 */
async function createTelegramContent(uploadedFiles, metadata) {
  try {
    // Since GraphQL had validation errors, just use REST directly based on sample data
    logger.info('Using REST API directly based on sample data format');
    
    // Generate a simple random document ID (similar to sample data)
    const documentId = [...Array(24)].map(() => 
      "abcdefghijklmnopqrstuvwxyz0123456789"[Math.floor(Math.random() * 36)]
    ).join('');
    
    // Use the data wrapper required by Strapi 5 and match the exact field name (Media capitalized)
    const payload = {
      data: {
        // Remove documentId as it's system-generated
        username: metadata.username,
        timestamp: new Date().toISOString(),
        media_type: metadata.mediaType,
        source: 'telegram',
        // Convert lat/long to strings as required by validation
        latitude: metadata.latitude?.toString() || null,
        longitude: metadata.longitude?.toString() || null,
        original_filename: metadata.originalFilename,
        // Use lowercase "media" with direct array of IDs, no connect object
        media: uploadedFiles.map(file => file.id.toString())
      }
    };
    
    logger.debug('REST payload with data wrapper:', JSON.stringify(payload, null, 2));
    
    try {
      const response = await axios.post(
        `${strapiUrl}/api/telegram-contents`, 
        payload, 
        {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${strapiApiToken}`,
          },
        }
      );
      
      logger.debug('Successfully created content:', JSON.stringify(response.data, null, 2));
      return response.data;
    } catch (error) {
      logger.warn('First attempt failed, trying with different media format', error.message);
      
      // Try with connect format
      const connectPayload = {
        data: {
          // Remove documentId as it's system-generated
          username: metadata.username,
          timestamp: new Date().toISOString(),
          media_type: metadata.mediaType,
          source: 'telegram',
          // Convert lat/long to strings as required by validation
          latitude: metadata.latitude?.toString() || null,
          longitude: metadata.longitude?.toString() || null,
          original_filename: metadata.originalFilename,
          // Use lowercase "media" with direct array of IDs, no connect object
          media: uploadedFiles.map(file => file.id.toString())
        }
      };
      
      try {
        const connectResponse = await axios.post(
          `${strapiUrl}/api/telegram-contents`, 
          connectPayload, 
          {
            headers: {
              'Content-Type': 'application/json',
              Authorization: `Bearer ${strapiApiToken}`,
            },
          }
        );
        
        logger.debug('Created with connect format:', JSON.stringify(connectResponse.data, null, 2));
        return connectResponse.data;
      } catch (connectError) {
        logger.warn('Connect format failed, trying two-step approach', connectError.message);
        
        // Try creating without media first - with data wrapper as required
        const basicPayload = {
          data: {
            // Remove documentId as it's system-generated
            username: metadata.username,
            timestamp: new Date().toISOString(),
            media_type: metadata.mediaType,
            source: 'telegram',
            // Convert lat/long to strings as required by validation
            latitude: metadata.latitude?.toString() || null,
            longitude: metadata.longitude?.toString() || null,
            original_filename: metadata.originalFilename
          }
        };
        
        const basicResponse = await axios.post(
          `${strapiUrl}/api/telegram-contents`, 
          basicPayload, 
          {
            headers: {
              'Content-Type': 'application/json',
              Authorization: `Bearer ${strapiApiToken}`,
            },
          }
        );
        
        logger.debug('Created basic content:', JSON.stringify(basicResponse.data, null, 2));
        
        // Then update with media if we have an ID
        if (basicResponse.data && basicResponse.data.id) {
          try {
            const contentId = basicResponse.data.id;
            const mediaPayload = {
              data: {
                // Use lowercase "media" with direct array of IDs, no connect object
                media: uploadedFiles.map(file => file.id.toString())
              }
            };
            
            logger.debug(`Updating content ${contentId} with media:`, JSON.stringify(mediaPayload, null, 2));
            
            const updateResponse = await axios.put(
              `${strapiUrl}/api/telegram-contents/${contentId}`, 
              mediaPayload, 
              {
                headers: {
                  'Content-Type': 'application/json',
                  Authorization: `Bearer ${strapiApiToken}`,
                },
              }
            );
            
            logger.debug('Updated with media:', JSON.stringify(updateResponse.data, null, 2));
            return updateResponse.data;
          } catch (updateError) {
            logger.error('Failed to update with media', updateError.message);
            return basicResponse.data; // Return basic content without media
          }
        }
        
        return basicResponse.data;
      }
    }
  } catch (error) {
    logger.error('All Strapi content creation attempts failed', error);
    if (error.response) {
      logger.error('Final error response:', JSON.stringify(error.response.data, null, 2));
    }
    throw error;
  }
}

module.exports = {
  uploadFileToStrapi,
  createTelegramContent
};