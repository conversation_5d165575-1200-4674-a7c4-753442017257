<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RacedayTV Timeline</title>
    <meta name="description" content="Social media timeline for RacedayTV fans">
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="timeline.css">
    
    <!-- Favicon -->
    <link rel="icon" href="data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 100 100%22><text y=%22.9em%22 font-size=%2290%22>🏁</text></svg>">
    
    <!-- iOS PWA Meta Tags -->
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="theme-color" content="#e44d26">
</head>
<body>
    <div class="app-container">
        <!-- Header Section -->
        <header class="app-header">
            <div class="header-content">
                <div class="logo-container">
                    <i class="fas fa-flag-checkered logo-icon"></i>
                    <h1>RacedayTV</h1>
                </div>
                <nav class="header-nav">
                    <button class="nav-button active">
                        <i class="fas fa-home"></i>
                        <span>Home</span>
                    </button>
                    <button class="nav-button">
                        <i class="fas fa-compass"></i>
                        <span>Explore</span>
                    </button>
                    <button class="nav-button">
                        <i class="fas fa-bell"></i>
                        <span>Notifications</span>
                    </button>
                    <button class="nav-button">
                        <i class="fas fa-user"></i>
                        <span>Profile</span>
                    </button>
                </nav>
            </div>
        </header>

        <!-- Main Content -->
        <main class="timeline-container">
            <!-- Stories Section -->
            <div class="stories-container">
                <div class="story">
                    <div class="story-avatar new-story">
                        <i class="fas fa-plus"></i>
                    </div>
                    <span>Your Story</span>
                </div>
                <!-- Additional stories would be dynamically inserted here -->
            </div>

            <!-- Timeline Posts -->
            <div id="timeline">
                <div class="initial-loader">
                    <div class="loader-spinner"></div>
                    <p>Loading latest posts...</p>
                </div>
                <!-- Posts will be dynamically inserted here by JavaScript -->
            </div>
        </main>

        <!-- Footer Section -->
        <footer class="app-footer">
            <div class="footer-content">
                <p class="copyright">© 2025 RacedayTV. All rights reserved.</p>
                <div class="footer-links">
                    <a href="#">About</a>
                    <a href="#">Help</a>
                    <a href="#">Terms</a>
                    <a href="#">Privacy</a>
                    <a href="#">Cookies</a>
                </div>
                <div class="social-links">
                    <a href="#" aria-label="Twitter"><i class="fab fa-twitter"></i></a>
                    <a href="#" aria-label="Instagram"><i class="fab fa-instagram"></i></a>
                    <a href="#" aria-label="Facebook"><i class="fab fa-facebook"></i></a>
                    <a href="#" aria-label="YouTube"><i class="fab fa-youtube"></i></a>
                </div>
            </div>
        </footer>
    </div>

    <!-- JavaScript -->
    <script src="timeline.js"></script>
    
    <!-- PWA Service Worker Registration (optional) -->
    <script>
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', () => {
                navigator.serviceWorker.register('/sw.js').then(registration => {
                    console.log('ServiceWorker registration successful');
                }).catch(err => {
                    console.log('ServiceWorker registration failed: ', err);
                });
            });
        }
    </script>
</body>
</html>