document.addEventListener('DOMContentLoaded', function() {
    const timeline = document.getElementById('timeline');
    const strapiUrl = "http://cdn1.provisual.media:1349";
    const strapiApiToken = "a7eeafba484951a0d09870949b242fa93024a2b7e8a4909a5b6a0a51b9427e4e784498ddeb98bbccc3350c1bf34ec6e26dc7db1b825f20d7b58ea79f1f125d31df9b8a2bbbcead025924af44e5639fb90399881ba46f4385e013844db4c83943f5cda010b665ff8efc7a715f89f97bbeae4c86d6f63392ab070eceb2fb80eacd";

    // State management
    let currentStart = 1;
    let totalPages = null;
    const postsPerPage = 20;
    let isFetching = false;
    let errorCount = 0;
    const scrollDebounceDelay = 200;
    let lastScrollTime = 0;
    let activePortraitVideo = null;

    // Initial fetch
    fetchTimelineData();

    // Scroll handling
    window.addEventListener('scroll', handleScroll);

    function handleScroll() {
        const now = Date.now();
        if (now - lastScrollTime < scrollDebounceDelay) return;
        lastScrollTime = now;

        if (isFetching) return;

        const { scrollTop, scrollHeight, clientHeight } = document.documentElement;
        const distanceToBottom = scrollHeight - (scrollTop + clientHeight);

        if (distanceToBottom < 500) {
            fetchMoreData();
        }
    }

    async function fetchMoreData() {
        if (isFetching || errorCount >= 3) return;
        
        isFetching = true;
        const loader = document.createElement('div');
        loader.className = 'loader';
        loader.textContent = 'Loading more posts...';
        timeline.appendChild(loader);

        try {
            await fetchTimelineData();
            errorCount = 0;
        } catch (error) {
            errorCount++;
            showError(errorCount < 3 ? 
                'Error loading posts. Trying again...' : 
                'Failed to load more posts. Please refresh the page.');
        } finally {
            isFetching = false;
            loader.remove();
        }
    }

    function showError(message) {
        const errorElement = document.createElement('div');
        errorElement.className = 'error-message';
        errorElement.textContent = message;
        timeline.appendChild(errorElement);
        setTimeout(() => errorElement.remove(), 3000);
    }

    async function fetchTimelineData() {
        if (totalPages !== null && currentStart > totalPages) return;

        try {
            const response = await fetch(`${strapiUrl}/api/telegram-contents?populate=*&pagination[page]=${currentStart}&pagination[pageSize]=${postsPerPage}`, {
                headers: { Authorization: `Bearer ${strapiApiToken}` }
            });

            if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);

            const data = await response.json();
            
            if (data?.data?.length) {
                renderTimelinePosts(data.data);
                currentStart++;
                totalPages = data.meta?.pagination?.pageCount;
            } else if (currentStart === 1) {
                timeline.innerHTML = '<p>No posts available.</p>';
            }
        } catch (error) {
            console.error("Error fetching timeline data:", error);
            throw error;
        }
    }

    function renderTimelinePosts(posts) {
        const fragment = document.createDocumentFragment();

        posts.forEach(post => {
            const postElement = createPostElement(post);
            fragment.appendChild(postElement);
        });

        timeline.appendChild(fragment);
        
        setTimeout(() => {
            initializeVideoPlayers();
            setupEventListeners();
        }, 0);
    }

    function createPostElement(post) {
        const postElement = document.createElement('div');
        postElement.classList.add('post');
        postElement.innerHTML = `
            ${createPostHeader(post)}
            ${createMediaContent(post)}
            ${createPostFooter(post)}
        `;
        return postElement;
    }

    function createPostHeader(post) {
        return `
            <div class="post-header">
                <div class="post-avatar">
                    <img src="https://ui-avatars.com/api/?name=${post.username || 'Anonymous'}&background=${encodeURIComponent('#e44d26')}&color=fff" alt="${post.username || 'Anonymous'}">
                </div>
                <div class="post-user">
                    <span class="post-username">${post.username || 'Anonymous'}</span>
                    <span class="post-time">${formatPostTime(post.createdAt)}</span>
                </div>
                <button class="post-more">
                    <i class="fas fa-ellipsis-h"></i>
                </button>
            </div>
        `;
    }

    function createMediaContent(post) {
        if (post.media_type === 'video') {
            const mediaUrl = post.media && post.media.length > 0 ? 
                `${strapiUrl}${post.media[0].url}` : 
                "https://placehold.co/600x400/CCC/31343C?text=User+Video&font=Montserrat";
            
            return `
                <div class="video-player-container">
                    <div class="video-wrapper">
                        <video class="modern-video-player" preload="metadata" playsinline>
                            <source src="${mediaUrl}" type="video/mp4">
                            Your browser does not support the video tag.
                        </video>
                        ${createVideoControls()}
                    </div>
                </div>
            `;
        } else if (post.media_type === 'photo') {
            const mediaUrl = post.media && post.media.length > 0 ? 
                `${strapiUrl}${post.media[0].url}` : 
                "https://placehold.co/600x400/CCC/31343C?text=User+Photo&font=Montserrat";
            return `<img src="${mediaUrl}" alt="Post Image" class="post-image">`;
        }
        return '<p class="unsupported-media">Unsupported media type</p>';
    }

    function createVideoControls() {
        return `
            <div class="video-controls">
                <div class="progress-container">
                    <div class="progress-bar"></div>
                    <div class="buffered-bar"></div>
                    <div class="time-rail">
                        <span class="current-time">0:00</span>
                        <span class="duration">0:00</span>
                    </div>
                </div>
                <div class="controls-bar">
                    <div class="left-controls">
                        <button class="control-button play-pause" aria-label="Play/Pause">
                            <i class="fas fa-play"></i>
                        </button>
                        <button class="control-button volume" aria-label="Volume">
                            <i class="fas fa-volume-up"></i>
                        </button>
                        <div class="volume-slider-container">
                            <input type="range" class="volume-slider" min="0" max="1" step="0.01" value="1">
                        </div>
                        <span class="time-display">0:00 / 0:00</span>
                    </div>
                    <div class="right-controls">
                        <button class="control-button playback-speed" aria-label="Playback Speed">1x</button>
                        <button class="control-button fullscreen" aria-label="Fullscreen">
                            <i class="fas fa-expand"></i>
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    function createPostFooter(post) {
        return `
            <div class="post-actions">
                <div class="action-group">
                    <button class="action-button like-button ${post.likes > 0 ? 'liked' : ''}" data-post-id="${post.id}">
                        <i class="fas fa-heart"></i>
                    </button>
                    <button class="action-button comment-button" data-post-id="${post.id}">
                        <i class="fas fa-comment"></i>
                    </button>
                    <button class="action-button share-button">
                        <i class="fas fa-share"></i>
                    </button>
                </div>
                <button class="action-button save-button">
                    <i class="fas fa-bookmark"></i>
                </button>
            </div>
            <div class="post-likes">${post.likes || 0} likes</div>
            <div class="post-caption">${post.caption || ''}</div>
            <div class="post-comments">
                <span class="view-all">View all comments</span>
            </div>
            <div class="comment-form-container" style="display: none;">
                <form class="comment-form" data-post-id="${post.id}">
                    <input type="text" class="comment-input" placeholder="Add a comment...">
                    <button type="submit" class="comment-submit" disabled>Post</button>
                </form>
            </div>
        `;
    }

    function initializeVideoPlayers() {
        document.querySelectorAll('.modern-video-player').forEach(video => {
            const container = video.closest('.video-player-container');
            const controls = container.querySelector('.video-controls');
            const playPauseBtn = container.querySelector('.play-pause');
            const volumeBtn = container.querySelector('.volume');
            const volumeSlider = container.querySelector('.volume-slider');
            const progressBar = container.querySelector('.progress-bar');
            const bufferedBar = container.querySelector('.buffered-bar');
            const timeDisplay = container.querySelector('.time-display');
            const currentTimeDisplay = container.querySelector('.current-time');
            const durationDisplay = container.querySelector('.duration');
            const playbackSpeedBtn = container.querySelector('.playback-speed');
            const fullscreenBtn = container.querySelector('.fullscreen');
            
            // Add close button for portrait mode
            const closeBtn = document.createElement('button');
            closeBtn.className = 'portrait-close-btn';
            closeBtn.innerHTML = '<i class="fas fa-times"></i>';
            container.appendChild(closeBtn);
            closeBtn.style.display = 'none';
            
            let controlsTimeout;
            const hideControlsDelay = 3000;
            
            // Detect video orientation
            video.addEventListener('loadedmetadata', function() {
                if (this.videoWidth && this.videoHeight) {
                    const isPortrait = this.videoHeight > this.videoWidth;
                    
                    if (isPortrait) {
                        container.classList.add('portrait');
                        
                        // Store state
                        let isInPortraitMode = false;
                        
                        // Instead of auto-activating on play, add a portrait mode toggle button
                        const portraitModeBtn = document.createElement('button');
                        portraitModeBtn.className = 'control-button portrait-mode-toggle';
                        portraitModeBtn.innerHTML = '<i class="fas fa-expand-alt"></i>';
                        portraitModeBtn.setAttribute('aria-label', 'Portrait Mode');
                        
                        // Add to the right controls section
                        const rightControls = container.querySelector('.right-controls');
                        const fullscreenBtn = container.querySelector('.fullscreen');
                        rightControls.insertBefore(portraitModeBtn, fullscreenBtn);
                        
                        // Toggle portrait mode on button click
                        portraitModeBtn.addEventListener('click', (e) => {
                            e.stopPropagation();
                            
                            if (!isInPortraitMode) {
                                // Close any currently active portrait video
                                if (activePortraitVideo && activePortraitVideo !== container) {
                                    exitPortraitMode(activePortraitVideo);
                                }
                                
                                // Activate this video
                                document.body.classList.add('portrait-video-open');
                                container.classList.add('portrait-active');
                                closeBtn.style.display = 'flex';
                                activePortraitVideo = container;
                                isInPortraitMode = true;
                                portraitModeBtn.innerHTML = '<i class="fas fa-compress-alt"></i>';
                            } else {
                                // Exit portrait mode
                                exitPortraitMode(container);
                                isInPortraitMode = false;
                                portraitModeBtn.innerHTML = '<i class="fas fa-expand-alt"></i>';
                            }
                        });
                        
                        // No longer auto-activate on play
                        
                        // Setup close handlers
                        closeBtn.addEventListener('click', (e) => {
                            e.stopPropagation();
                            exitPortraitMode(container);
                            isInPortraitMode = false; // Reset state
                        });
                        
                        // Only close when clicking directly on container or video
                        // to avoid closing when clicking on controls
                        container.addEventListener('click', (e) => {
                            if (e.target === container || e.target === video) {
                                exitPortraitMode(container);
                                isInPortraitMode = false; // Reset state
                            }
                        });
                        
                        // Handle orientation changes
                        const orientationHandler = () => {
                            if (isInPortraitMode) {
                                // Adjust layout for new orientation if needed
                                setTimeout(() => {
                                    // Allow time for the browser to update dimensions
                                    if (window.innerWidth > window.innerHeight) {
                                        // Landscape mode - consider exiting portrait mode
                                        exitPortraitMode(container);
                                        isInPortraitMode = false;
                                    }
                                }, 300);
                            }
                        };
                        
                        window.addEventListener('orientationchange', orientationHandler);
                        window.addEventListener('resize', orientationHandler);
                        
                        // Clean up event listeners when video is removed from DOM
                        const observer = new MutationObserver((mutations) => {
                            mutations.forEach((mutation) => {
                                if (mutation.removedNodes) {
                                    mutation.removedNodes.forEach((node) => {
                                        if (node === container || node.contains(container)) {
                                            window.removeEventListener('orientationchange', orientationHandler);
                                            window.removeEventListener('resize', orientationHandler);
                                            observer.disconnect();
                                        }
                                    });
                                }
                            });
                        });
                        
                        observer.observe(document.body, { childList: true, subtree: true });
                    }
                }
            });
            
            // Play/Pause functionality
            playPauseBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                togglePlayPause();
            });
            
            function togglePlayPause() {
                if (video.paused) {
                    video.play().catch(e => console.error("Play failed:", e));
                    playPauseBtn.innerHTML = '<i class="fas fa-pause"></i>';
                } else {
                    video.pause();
                    playPauseBtn.innerHTML = '<i class="fas fa-play"></i>';
                }
                showControls();
            }
            
            // Video events
            video.addEventListener('play', () => {
                playPauseBtn.innerHTML = '<i class="fas fa-pause"></i>';
                controls.classList.add('visible');
                startControlsTimer();
            });
            
            video.addEventListener('pause', () => {
                playPauseBtn.innerHTML = '<i class="fas fa-play"></i>';
                clearTimeout(controlsTimeout);
            });
            
            video.addEventListener('click', togglePlayPause);
            
            // Volume controls
            volumeBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                if (video.volume > 0) {
                    video.volume = 0;
                    volumeBtn.innerHTML = '<i class="fas fa-volume-mute"></i>';
                    volumeSlider.value = 0;
                } else {
                    video.volume = volumeSlider.value || 1;
                    updateVolumeIcon();
                }
            });
            
            volumeSlider.addEventListener('input', () => {
                video.volume = volumeSlider.value;
                updateVolumeIcon();
            });
            
            function updateVolumeIcon() {
                if (video.volume === 0) {
                    volumeBtn.innerHTML = '<i class="fas fa-volume-mute"></i>';
                } else if (video.volume < 0.5) {
                    volumeBtn.innerHTML = '<i class="fas fa-volume-down"></i>';
                } else {
                    volumeBtn.innerHTML = '<i class="fas fa-volume-up"></i>';
                }
            }
            
            // Progress bar
            video.addEventListener('timeupdate', updateProgress);
            
            function updateProgress() {
                if (!isNaN(video.duration)) {
                    const percent = (video.currentTime / video.duration) * 100;
                    progressBar.style.width = `${percent}%`;
                    
                    currentTimeDisplay.textContent = formatTime(video.currentTime);
                    timeDisplay.textContent = `${formatTime(video.currentTime)} / ${formatTime(video.duration)}`;
                }
            }
            
            video.addEventListener('progress', updateBuffered);
            
            function updateBuffered() {
                if (video.buffered.length > 0 && !isNaN(video.duration)) {
                    const bufferedEnd = video.buffered.end(video.buffered.length - 1);
                    const percent = (bufferedEnd / video.duration) * 100;
                    bufferedBar.style.width = `${percent}%`;
                }
            }
            
            video.addEventListener('loadedmetadata', updateDuration);
            
            function updateDuration() {
                if (!isNaN(video.duration)) {
                    durationDisplay.textContent = formatTime(video.duration);
                    timeDisplay.textContent = `0:00 / ${formatTime(video.duration)}`;
                }
            }
            
            progressBar.parentElement.addEventListener('click', seekVideo);
            
            function seekVideo(e) {
                if (!isNaN(video.duration)) {
                    const rect = e.target.getBoundingClientRect();
                    const pos = (e.clientX - rect.left) / rect.width;
                    video.currentTime = pos * video.duration;
                }
            }
            
            // Playback speed
            const speeds = [0.5, 0.75, 1, 1.25, 1.5, 2];
            let currentSpeedIndex = 2; // Default to 1x
            
            playbackSpeedBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                currentSpeedIndex = (currentSpeedIndex + 1) % speeds.length;
                video.playbackRate = speeds[currentSpeedIndex];
                playbackSpeedBtn.textContent = `${speeds[currentSpeedIndex]}x`;
            });
            
            // Fullscreen
            fullscreenBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                toggleFullscreen();
            });
            
            function toggleFullscreen() {
                if (!document.fullscreenElement) {
                    container.requestFullscreen().catch(err => {
                        console.error(`Fullscreen error: ${err.message}`);
                    });
                    fullscreenBtn.innerHTML = '<i class="fas fa-compress"></i>';
                } else {
                    document.exitFullscreen();
                    fullscreenBtn.innerHTML = '<i class="fas fa-expand"></i>';
                }
            }
            
            // Controls visibility
            container.addEventListener('mousemove', showControls);
            container.addEventListener('mouseleave', hideControlsIfPlaying);
            
            function showControls() {
                controls.classList.add('visible');
                clearTimeout(controlsTimeout);
                if (!video.paused) {
                    startControlsTimer();
                }
            }
            
            function hideControlsIfPlaying() {
                if (!video.paused) {
                    startControlsTimer();
                }
            }
            
            function startControlsTimer() {
                controlsTimeout = setTimeout(() => {
                    if (!video.paused) {
                        controls.classList.remove('visible');
                    }
                }, hideControlsDelay);
            }
        });
    }

    function exitPortraitMode(container) {
        if (!container) return; // Safety check
        
        const video = container.querySelector('video');
        const closeBtn = container.querySelector('.portrait-close-btn');
        const playPauseBtn = container.querySelector('.play-pause');
        
        if (video) video.pause();
        document.body.classList.remove('portrait-video-open');
        container.classList.remove('portrait-active');
        if (closeBtn) closeBtn.style.display = 'none';
        if (playPauseBtn) playPauseBtn.innerHTML = '<i class="fas fa-play"></i>';
        activePortraitVideo = null;
    }

    function setupEventListeners() {
        // Like button functionality
        timeline.addEventListener('click', handleLike);
        
        function handleLike(event) {
            if (event.target.closest('.like-button')) {
                const likeButton = event.target.closest('.like-button');
                const postId = likeButton.dataset.postId;
                const postElement = likeButton.closest('.post');
                const likesCount = postElement.querySelector('.post-likes');
                
                likeButton.classList.toggle('liked');
                const isLiked = likeButton.classList.contains('liked');
                
                const currentLikes = parseInt(likesCount.textContent) || 0;
                likesCount.textContent = `${isLiked ? currentLikes + 1 : currentLikes - 1} likes`;
                
                updateLikeCount(postId, isLiked, currentLikes).catch(error => {
                    console.error('Error updating like count:', error);
                    likeButton.classList.toggle('liked');
                    likesCount.textContent = `${currentLikes} likes`;
                });
            }
        }
        
        async function updateLikeCount(postId, isLiked, currentLikes) {
            const response = await fetch(`${strapiUrl}/api/telegram-contents/${postId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${strapiApiToken}`,
                },
                body: JSON.stringify({ 
                    data: { likes: isLiked ? currentLikes + 1 : currentLikes - 1 } 
                }),
            });

            if (!response.ok) {
                throw new Error('Failed to update like count');
            }
        }

        // Comment button toggle
        timeline.addEventListener('click', (event) => {
            if (event.target.closest('.comment-button')) {
                const commentButton = event.target.closest('.comment-button');
                const postElement = commentButton.closest('.post');
                const commentForm = postElement.querySelector('.comment-form-container');
                commentForm.style.display = commentForm.style.display === 'none' ? 'block' : 'none';
                
                if (commentForm.style.display === 'block') {
                    commentForm.querySelector('.comment-input').focus();
                }
            }
        });

        // Comment form submission
        timeline.addEventListener('submit', handleCommentSubmit);
        
        function handleCommentSubmit(event) {
            if (event.target.classList.contains('comment-form')) {
                event.preventDefault();
                const form = event.target;
                const postId = form.dataset.postId;
                const commentInput = form.querySelector('.comment-input');
                const commentText = commentInput.value.trim();
                const postElement = form.closest('.post');
                const commentsSection = postElement.querySelector('.post-comments');
                
                if (!commentText) return;
                
                const tempComment = document.createElement('div');
                tempComment.textContent = commentText;
                commentsSection.insertBefore(tempComment, commentsSection.querySelector('.view-all'));
                
                commentInput.value = '';
                form.querySelector('.comment-submit').disabled = true;
                
                postComment(postId, commentText)
                    .then(() => {
                        // Optional: Update with actual comment data from response
                    })
                    .catch(error => {
                        console.error('Error posting comment:', error);
                        tempComment.remove();
                    });
            }
        }
        
        async function postComment(postId, commentText) {
            const response = await fetch(`${strapiUrl}/api/comments`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${strapiApiToken}`,
                },
                body: JSON.stringify({ 
                    data: { 
                        content: commentText, 
                        telegramContent: postId 
                    } 
                }),
            });

            if (!response.ok) {
                throw new Error('Failed to post comment');
            }
            return await response.json();
        }

        // Enable/disable comment submit button
        timeline.addEventListener('input', (event) => {
            if (event.target.classList.contains('comment-input')) {
                const submitButton = event.target.closest('.comment-form').querySelector('.comment-submit');
                submitButton.disabled = event.target.value.trim() === '';
            }
        });
    }

    function formatPostTime(timestamp) {
        if (!timestamp) return 'Just now';
        
        const postDate = new Date(timestamp);
        const now = new Date();
        const diffInSeconds = Math.floor((now - postDate) / 1000);
        
        if (diffInSeconds < 60) return 'Just now';
        if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
        if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
        
        return postDate.toLocaleDateString('en-US', { 
            month: 'short', 
            day: 'numeric',
            year: diffInSeconds > 31536000 ? 'numeric' : undefined
        });
    }

    function formatTime(seconds) {
        if (isNaN(seconds)) return '0:00';
        
        const minutes = Math.floor(seconds / 60);
        const secs = Math.floor(seconds % 60);
        return `${minutes}:${secs < 10 ? '0' : ''}${secs}`;
    }
});
