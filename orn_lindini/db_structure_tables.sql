CREATE TABLE events (
    id SERIAL PRIMARY KEY,
    name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    description TEXT,
    start_date TIMESTAMP,
    end_date TIMESTAMP,
    location VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMES<PERSON>MP DEFAULT CURRENT_TIMESTAMP
);


CREATE TABLE teams (
    id SERIAL PRIMARY KEY,
    event_id INTEGER REFERENCES events(id),
    race_number VARCHAR(50) NOT NULL,
    team_name VARCHAR(255) NOT NULL,
    participant1_name VARCHAR(255),
    participant2_name VARCHAR(255),
    original_names_string TEXT,
    safe_filename VARCHAR(255),
    has_special_chars BOOLEAN DEFAULT FALSE,
    has_apostrophes BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(event_id, race_number)
);


CREATE TABLE tracking_sessions (
    id SERIAL PRIMARY KEY,
    team_id INTEGER REFERENCES teams(id),
    session_name VARCHA<PERSON>(255),
    description TEXT,
    start_time TIMESTAMP,
    end_time TIMESTAMP,
    total_records INTEGER DEFAULT 0,
    data_source VARCHAR(100), -- 'api', 'manual', 'import'
    api_url TEXT,
    status VARCHAR(50) DEFAULT 'active', -- 'active', 'completed', 'failed'
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);


CREATE TABLE gps_points (
    id BIGSERIAL PRIMARY KEY,
    session_id INTEGER REFERENCES tracking_sessions(id),
    timestamp TIMESTAMP NOT NULL,
    latitude DECIMAL(10, 7) NOT NULL,
    longitude DECIMAL(10, 7) NOT NULL,
    speed INTEGER DEFAULT 0, -- km/h
    altitude INTEGER, -- meters
    mileage DECIMAL(10, 2), -- total distance
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_session_timestamp (session_id, timestamp),
    INDEX idx_location (latitude, longitude),
    INDEX idx_timestamp (timestamp)
);


CREATE TABLE api_responses (
    id SERIAL PRIMARY KEY,
    session_id INTEGER REFERENCES tracking_sessions(id),
    request_url TEXT,
    status_code INTEGER,
    content_type VARCHAR(100),
    content_length INTEGER,
    content_encoding VARCHAR(50),
    is_compressed BOOLEAN DEFAULT FALSE,
    data_type VARCHAR(50), -- 'json', 'text', 'empty'
    response_timestamp TIMESTAMP,
    raw_headers JSONB,
    parse_errors TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE character_mappings (
    id SERIAL PRIMARY KEY,
    team_id INTEGER REFERENCES teams(id),
    original_text TEXT,
    safe_text VARCHAR(255),
    mapping_type VARCHAR(50), -- 'filename', 'display', 'url'
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);


CREATE TABLE character_mappings (
    id SERIAL PRIMARY KEY,
    team_id INTEGER REFERENCES teams(id),
    original_text TEXT,
    safe_text VARCHAR(255),
    mapping_type VARCHAR(50), -- 'filename', 'display', 'url'
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);


CREATE TABLE processing_logs (
    id SERIAL PRIMARY KEY,
    session_id INTEGER REFERENCES tracking_sessions(id),
    process_type VARCHAR(100), -- 'import', 'validation', 'cleanup'
    status VARCHAR(50), -- 'started', 'completed', 'failed'
    records_processed INTEGER DEFAULT 0,
    errors_count INTEGER DEFAULT 0,
    processing_time_ms INTEGER,
    error_details JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);


CREATE VIEW latest_positions AS
SELECT DISTINCT ON (s.team_id)
    t.race_number,
    t.team_name,
    gp.timestamp,
    gp.latitude,
    gp.longitude,
    gp.speed,
    gp.altitude,
    gp.mileage
FROM gps_points gp
JOIN tracking_sessions s ON gp.session_id = s.id
JOIN teams t ON s.team_id = t.id
ORDER BY s.team_id, gp.timestamp DESC;


CREATE VIEW team_summary AS
SELECT 
    t.id,
    t.race_number,
    t.team_name,
    t.participant1_name,
    t.participant2_name,
    COUNT(DISTINCT s.id) as session_count,
    COUNT(gp.id) as total_gps_points,
    MIN(gp.timestamp) as first_point,
    MAX(gp.timestamp) as last_point,
    MAX(gp.mileage) as total_distance
FROM teams t
LEFT JOIN tracking_sessions s ON t.id = s.team_id
LEFT JOIN gps_points gp ON s.id = gp.session_id
GROUP BY t.id, t.race_number, t.team_name, t.participant1_name, t.participant2_name;


ALTER TABLE tracking_sessions ADD COLUMN raw_data JSONB;
CREATE INDEX idx_raw_data_gin ON tracking_sessions USING GIN (raw_data);
