// competitors-tracking-importer-with-enhanced-character-handling.js
const fs = require('fs');
const path = require('path');
const https = require('https');
const http = require('http');
const zlib = require('zlib');
const { promisify } = require('util');

class CompetitorsTrackingImporter {
    constructor() {
        this.competitors = [];
        this.outputDir = './competitors_data';
        this.trackingDir = './tracking_data';
        this.rawDir = './raw_data';
        this.processedDir = './processed_data';
        this.requestDelay = 1000; // 1 second delay between requests
        this.maxRetries = 3;
        this.characterMappings = new Map(); // Store original -> safe mappings
        this.ensureDirectories();
    }

    ensureDirectories() {
        [this.outputDir, this.trackingDir, this.rawDir, this.processedDir].forEach(dir => {
            if (!fs.existsSync(dir)) {
                fs.mkdirSync(dir, { recursive: true });
                console.log(`Created directory: ${dir}`);
            }
        });
    }

    /**
     * Enhanced special character handling methods
     */
    normalizeUnicode(text) {
        // Normalize Unicode characters to NFC form
        return text.normalize('NFC');
    }

    removeAccents(text) {
        // Remove accents while preserving the base character
        return text.normalize('NFD').replace(/[\u0300-\u036f]/g, '');
    }

    createSafeFileName(originalText) {
        // Create a safe filename while preserving as much information as possible
        let safe = originalText;
        
        // Character substitution map for common special characters
        const substitutions = {
            'á': 'a', 'à': 'a', 'â': 'a', 'ä': 'a', 'ā': 'a', 'ă': 'a', 'ą': 'a',
            'é': 'e', 'è': 'e', 'ê': 'e', 'ë': 'e', 'ē': 'e', 'ĕ': 'e', 'ę': 'e',
            'í': 'i', 'ì': 'i', 'î': 'i', 'ï': 'i', 'ī': 'i', 'ĭ': 'i', 'į': 'i',
            'ó': 'o', 'ò': 'o', 'ô': 'o', 'ö': 'o', 'ō': 'o', 'ŏ': 'o', 'ő': 'o',
            'ú': 'u', 'ù': 'u', 'û': 'u', 'ü': 'u', 'ū': 'u', 'ŭ': 'u', 'ů': 'u',
            'ç': 'c', 'ć': 'c', 'ĉ': 'c', 'ċ': 'c', 'č': 'c',
            'ñ': 'n', 'ń': 'n', 'ň': 'n', 'ņ': 'n',
            'ý': 'y', 'ÿ': 'y', 'ŷ': 'y',
            'ş': 's', 'ś': 's', 'ŝ': 's', 'š': 's',
            'ğ': 'g', 'ĝ': 'g', 'ğ': 'g', 'ġ': 'g',
            'ł': 'l', 'ĺ': 'l', 'ļ': 'l', 'ľ': 'l',
            'ř': 'r', 'ŕ': 'r', 'ŗ': 'r',
            'ž': 'z', 'ź': 'z', 'ż': 'z',
            'đ': 'd', 'ď': 'd',
            'ť': 't', 'ţ': 't',
            // Common South African and Afrikaans characters
            'ê': 'e', 'ô': 'o', 'û': 'u', 'î': 'i',
            // Preserve apostrophes in a safe way
            "'": '_apos_',
            "'": '_apos_',
            "'": '_apos_'
        };

        // Apply substitutions
        Object.entries(substitutions).forEach(([char, replacement]) => {
            safe = safe.replace(new RegExp(char, 'g'), replacement);
        });

        // Store the mapping for potential reversal
        this.characterMappings.set(safe, originalText);

        // Replace any remaining special characters with underscores
        safe = safe.replace(/[^a-zA-Z0-9_]/g, '_');
        
        // Clean up multiple underscores
        safe = safe.replace(/_+/g, '_');
        
        // Remove leading/trailing underscores
        safe = safe.replace(/^_|_$/g, '');

        return safe;
    }

    parseEnhancedRaceNumber(text) {
        // Enhanced race number parsing to handle various formats
        // Examples: 205, T8, A421, G2, CC2, etc.
        const raceNumberMatch = text.match(/^([A-Z]*\d+[A-Z]*)/);
        return raceNumberMatch ? raceNumberMatch[1] : null;
    }

    splitNamesIntelligently(namesString) {
        // Enhanced name splitting that handles special characters
        const normalizedNames = this.normalizeUnicode(namesString);
        const nameWords = normalizedNames.split(/\s+/);
        
        let participant1 = '';
        let participant2 = '';
        
        if (nameWords.length >= 2) {
            // Look for common patterns that might indicate name boundaries
            let splitIndex = Math.ceil(nameWords.length / 2);
            
            // Check for obvious surname patterns (van, de, du, etc.)
            for (let i = 1; i < nameWords.length - 1; i++) {
                const word = nameWords[i].toLowerCase();
                if (['van', 'de', 'du', 'le', 'la', 'von', 'der', 'den'].includes(word)) {
                    // If we find a surname prefix, it likely starts participant 2
                    if (i > nameWords.length / 3) { // Not too early in the string
                        splitIndex = i;
                        break;
                    }
                }
            }
            
            participant1 = nameWords.slice(0, splitIndex).join(' ');
            participant2 = nameWords.slice(splitIndex).join(' ');
        } else {
            participant1 = namesString;
        }

        return {
            participant1: participant1.trim(),
            participant2: participant2.trim(),
            originalString: namesString
        };
    }

    validateAndLogCharacterHandling(originalText, processedText) {
        // Log character transformations for debugging
        const hasSpecialChars = /[^\x00-\x7F]/.test(originalText);
        const hasApostrophes = /['']/.test(originalText);
        
        if (hasSpecialChars || hasApostrophes) {
            console.log(`Character transformation: "${originalText}" -> "${processedText}"`);
        }
        
        return {
            hasSpecialChars,
            hasApostrophes,
            originalLength: originalText.length,
            processedLength: processedText.length
        };
    }

    parseRawData(rawData) {
        const lines = rawData.trim().split('\n');
        const competitors = [];
        let parseErrors = [];

        for (let i = 0; i < lines.length; i++) {
            const line = lines[i];
            if (line.trim().startsWith('-')) {
                try {
                    const competitor = this.parseSingleCompetitor(line, i + 1);
                    if (competitor) {
                        competitors.push(competitor);
                    }
                } catch (error) {
                    parseErrors.push({
                        line: i + 1,
                        text: line,
                        error: error.message
                    });
                }
            }
        }

        if (parseErrors.length > 0) {
            console.warn(`Parse errors encountered on ${parseErrors.length} lines:`);
            parseErrors.forEach(err => {
                console.warn(`  Line ${err.line}: ${err.error}`);
            });
        }

        this.competitors = competitors;
        console.log(`Successfully parsed ${competitors.length} competitors with enhanced character handling`);
        return competitors;
    }

    parseSingleCompetitor(line, lineNumber = 0) {
        // Enhanced parsing with better special character support
        const cleanLine = line.replace(/^-\s*/, '').trim();
        
        // More flexible regex that handles various race number formats and special characters
        const enhancedMatch = cleanLine.match(/^([A-Z]*\d+[A-Z]*)\s*-\s*(.+?)(?=\s+\d{2}:\d{2}:\d{2}\.\d|$)/);
        
        if (!enhancedMatch) {
            console.warn(`Line ${lineNumber}: Could not parse race number and names from: "${cleanLine}"`);
            return null;
        }

        const raceNumber = enhancedMatch[1];
        const namesString = enhancedMatch[2].trim();
        
        // Enhanced name processing
        const nameInfo = this.splitNamesIntelligently(namesString);
        
        // Extract timing and numeric data
        const times = cleanLine.match(/\d{2}:\d{2}:\d{2}\.\d/g) || [];
        const numericValues = cleanLine.match(/\d+\.\d+/g) || [];
        const integers = cleanLine.match(/\b\d+\b/g) || [];

        // Create team name with proper character handling
        const teamName = `${raceNumber} - ${nameInfo.participant1} ${nameInfo.participant2}`.trim();
        
        // Create safe filename with enhanced character handling
        const safeRaceNumber = this.createSafeFileName(raceNumber);
        const safeParticipant1 = this.createSafeFileName(nameInfo.participant1);
        const safeParticipant2 = this.createSafeFileName(nameInfo.participant2);
        
        const safeFileName = `${safeRaceNumber}_${safeParticipant1}_${safeParticipant2}`;
        
        // Validate character handling
        const charValidation = this.validateAndLogCharacterHandling(teamName, safeFileName);

        return {
            raceNumber,
            participant1: nameInfo.participant1,
            participant2: nameInfo.participant2,
            teamName,
            originalNamesString: nameInfo.originalString,
            times,
            numericValues,
            integers,
            rawData: cleanLine,
            fileName: `${safeFileName}.txt`,
            trackingFileName: `${safeFileName}_tracking.json`,
            rawFileName: `${safeFileName}_raw.json`,
            processedFileName: `${safeFileName}_processed.json`,
            url: this.generateTrackingUrl(teamName),
            characterHandling: {
                safeFileName,
                originalTeamName: teamName,
                validation: charValidation
            }
        };
    }

    generateTrackingUrl(teamName, timestart = 1751698800, timeend = 1751729999) {
        const baseUrl = "https://data.africanstuff.co.za/l/akude_pull.php";
        
        // Enhanced URL encoding with proper character handling
        const normalizedName = this.normalizeUnicode(teamName);
        const encodedName = encodeURIComponent(normalizedName);
        
        // Log URL encoding for debugging
        if (teamName !== normalizedName) {
            console.log(`URL encoding: "${teamName}" -> "${normalizedName}" -> "${encodedName}"`);
        }
        
        return `${baseUrl}?objname=${encodedName}&timestart=${timestart}&timeend=${timeend}`;
    }

    async decompressGzipData(compressedData) {
        try {
            // Convert the unicode string back to buffer
            const buffer = Buffer.from(compressedData, 'binary');
            
            // Check if it's gzip compressed (starts with 0x1f, 0x8b)
            if (buffer.length >= 2 && buffer[0] === 0x1f && buffer[1] === 0x8b) {
                const gunzip = promisify(zlib.gunzip);
                const decompressed = await gunzip(buffer);
                return decompressed.toString('utf8');
            } else {
                // Not gzip compressed, return as is
                return compressedData;
            }
        } catch (error) {
            console.error('Decompression error:', error.message);
            return compressedData; // Return original if decompression fails
        }
    }

    async fetchTrackingData(url, retries = 0) {
        return new Promise((resolve, reject) => {
            const urlObj = new URL(url);
            const client = urlObj.protocol === 'https:' ? https : http;
            
            const options = {
                hostname: urlObj.hostname,
                port: urlObj.port,
                path: urlObj.pathname + urlObj.search,
                method: 'GET',
                headers: {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                    'Accept': 'application/json, text/plain, */*',
                    'Accept-Language': 'en-US,en;q=0.9',
                    'Accept-Encoding': 'gzip, deflate, br',
                    'Connection': 'keep-alive',
                    'Cache-Control': 'no-cache',
                    'Accept-Charset': 'utf-8, iso-8859-1;q=0.5'
                },
                timeout: 30000
            };

            const req = client.request(options, (res) => {
                let chunks = [];

                res.on('data', (chunk) => {
                    chunks.push(chunk);
                });

                res.on('end', async () => {
                    try {
                        const buffer = Buffer.concat(chunks);
                        let data = buffer.toString('utf8');

                        // Handle different encodings
                        if (res.headers['content-encoding'] === 'gzip') {
                            try {
                                const gunzip = promisify(zlib.gunzip);
                                const decompressed = await gunzip(buffer);
                                data = decompressed.toString('utf8');
                            } catch (gzipError) {
                                console.log('Gzip decompression failed, trying manual decompression');
                                data = await this.decompressGzipData(buffer.toString('binary'));
                            }
                        }

                        const result = {
                            statusCode: res.statusCode,
                            headers: res.headers,
                            rawData: buffer.toString('binary'),
                            data: data,
                            contentType: res.headers['content-type'] || 'unknown',
                            contentLength: buffer.length,
                            contentEncoding: res.headers['content-encoding'] || 'none',
                            timestamp: new Date().toISOString(),
                            isCompressed: res.headers['content-encoding'] === 'gzip' || this.isGzipData(buffer)
                        };

                        // Try to parse as JSON if it looks like JSON
                        if (data.trim().startsWith('{') || data.trim().startsWith('[')) {
                            try {
                                result.parsedData = JSON.parse(data);
                                result.dataType = 'json';
                            } catch (e) {
                                result.dataType = 'text';
                                result.parseError = e.message;
                            }
                        } else if (data.trim().length > 0) {
                            result.dataType = 'text';
                            // Try to extract meaningful data patterns
                            result.extractedData = this.extractDataPatterns(data);
                        } else {
                            result.dataType = 'empty';
                        }

                        resolve(result);
                    } catch (error) {
                        reject(error);
                    }
                });
            });

            req.on('error', (error) => {
                if (retries < this.maxRetries) {
                    console.log(`Retrying request (${retries + 1}/${this.maxRetries}): ${url}`);
                    setTimeout(() => {
                        this.fetchTrackingData(url, retries + 1)
                            .then(resolve)
                            .catch(reject);
                    }, (retries + 1) * 2000);
                } else {
                    reject(error);
                }
            });

            req.on('timeout', () => {
                req.destroy();
                reject(new Error('Request timeout'));
            });

            req.end();
        });
    }

    isGzipData(buffer) {
        return buffer.length >= 2 && buffer[0] === 0x1f && buffer[1] === 0x8b;
    }

    extractDataPatterns(data) {
        const patterns = {
            coordinates: [],
            timestamps: [],
            speeds: [],
            distances: [],
            other: []
        };

        // Look for coordinate patterns (lat/lng)
        const coordRegex = /-?\d+\.\d{4,}/g;
        const coords = data.match(coordRegex);
        if (coords) {
            patterns.coordinates = coords.slice(0, 10); // First 10 coordinates
        }

        // Look for timestamp patterns
        const timestampRegex = /\d{10,13}/g;
        const timestamps = data.match(timestampRegex);
        if (timestamps) {
            patterns.timestamps = timestamps.slice(0, 5).map(ts => {
                const date = new Date(parseInt(ts) * (ts.length === 10 ? 1000 : 1));
                return { raw: ts, formatted: date.toISOString() };
            });
        }

        // Look for speed patterns
        const speedRegex = /\b\d{1,3}\.\d{1,2}\b/g;
        const speeds = data.match(speedRegex);
        if (speeds) {
            patterns.speeds = speeds.slice(0, 10);
        }

        return patterns;
    }

    async delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    generateCompetitorReport(competitor, trackingResult) {
        const report = `
COMPETITOR REPORT
================

Race Number: ${competitor.raceNumber}
Team Name: ${competitor.teamName}
Participant 1: ${competitor.participant1}
Participant 2: ${competitor.participant2}

CHARACTER HANDLING
------------------
Original Names String: ${competitor.originalNamesString}
Safe Filename: ${competitor.characterHandling.safeFileName}
Has Special Characters: ${competitor.characterHandling.validation.hasSpecialChars}
Has Apostrophes: ${competitor.characterHandling.validation.hasApostrophes}

TIMING DATA
-----------
${competitor.times.map((time, index) => `Time ${index + 1}: ${time}`).join('\n')}

NUMERIC DATA
------------
${competitor.numericValues.map((val, index) => `Value ${index + 1}: ${val}`).join('\n')}

ADDITIONAL DATA
---------------
${competitor.integers.map((val, index) => `Integer ${index + 1}: ${val}`).join('\n')}

TRACKING DATA SUMMARY
---------------------
API URL: ${competitor.url}
Data Type: ${trackingResult?.dataType || 'unknown'}
Content Length: ${trackingResult?.contentLength || 0} bytes
Compressed: ${trackingResult?.isCompressed || false}
Status Code: ${trackingResult?.statusCode || 'unknown'}

${trackingResult?.extractedData ? `
EXTRACTED PATTERNS
------------------
Coordinates: ${trackingResult.extractedData.coordinates.length} found
Timestamps: ${trackingResult.extractedData.timestamps.length} found
Speeds: ${trackingResult.extractedData.speeds.length} found

Sample Coordinates: ${trackingResult.extractedData.coordinates.slice(0, 3).join(', ')}
Sample Timestamps: ${trackingResult.extractedData.timestamps.slice(0, 2).map(t => t.formatted).join(', ')}
Sample Speeds: ${trackingResult.extractedData.speeds.slice(0, 3).join(', ')}
` : ''}

RAW DATA
--------
${competitor.rawData}

Generated on: ${new Date().toISOString()}
`.trim();

        return report;
    }

    async fetchAllTrackingData() {
        console.log(`Fetching tracking data for ${this.competitors.length} competitors with enhanced character handling...`);
        
        const results = {
            successful: [],
            failed: [],
            totalRequests: this.competitors.length,
            startTime: new Date().toISOString(),
            characterHandlingStats: {
                totalSpecialChars: 0,
                totalApostrophes: 0,
                encodingIssues: 0
            }
        };

        for (let i = 0; i < this.competitors.length; i++) {
            const competitor = this.competitors[i];
            const progress = `${i + 1}/${this.competitors.length}`;
            
            // Update character handling stats
            if (competitor.characterHandling.validation.hasSpecialChars) {
                results.characterHandlingStats.totalSpecialChars++;
            }
            if (competitor.characterHandling.validation.hasApostrophes) {
                results.characterHandlingStats.totalApostrophes++;
            }
            
            try {
                console.log(`[${progress}] Fetching: ${competitor.raceNumber} - ${competitor.teamName}`);
                
                // Fetch tracking data
                const trackingData = await this.fetchTrackingData(competitor.url);
                
                // Save raw response
                const rawPath = path.join(this.rawDir, competitor.rawFileName);
                await fs.promises.writeFile(rawPath, JSON.stringify(trackingData, null, 2), 'utf8');
                
                // Save processed data if we have meaningful extracted data
                if (trackingData.extractedData) {
                    const processedPath = path.join(this.processedDir, competitor.processedFileName);
                    await fs.promises.writeFile(processedPath, JSON.stringify({
                        raceNumber: competitor.raceNumber,
                        teamName: competitor.teamName,
                        participant1: competitor.participant1,
                        participant2: competitor.participant2,
                        originalNamesString: competitor.originalNamesString,
                        characterHandling: competitor.characterHandling,
                        timestamp: trackingData.timestamp,
                        extractedData: trackingData.extractedData,
                        dataType: trackingData.dataType,
                        isCompressed: trackingData.isCompressed
                    }, null, 2), 'utf8');
                }
                
                // Save tracking data (original format)
                const trackingPath = path.join(this.trackingDir, competitor.trackingFileName);
                await fs.promises.writeFile(trackingPath, JSON.stringify(trackingData, null, 2), 'utf8');
                
                // Save competitor report
                const report = this.generateCompetitorReport(competitor, trackingData);
                const reportPath = path.join(this.outputDir, competitor.fileName);
                await fs.promises.writeFile(reportPath, report, 'utf8');
                
                results.successful.push({
                    raceNumber: competitor.raceNumber,
                    teamName: competitor.teamName,
                    originalNamesString: competitor.originalNamesString,
                    hasSpecialChars: competitor.characterHandling.validation.hasSpecialChars,
                    hasApostrophes: competitor.characterHandling.validation.hasApostrophes,
                    trackingFile: competitor.trackingFileName,
                    reportFile: competitor.fileName,
                    dataSize: trackingData.contentLength,
                    statusCode: trackingData.statusCode,
                    dataType: trackingData.dataType,
                    isCompressed: trackingData.isCompressed,
                    hasExtractedData: !!trackingData.extractedData,
                    coordinateCount: trackingData.extractedData?.coordinates?.length || 0,
                    timestampCount: trackingData.extractedData?.timestamps?.length || 0
                });
                
                console.log(`  ✓ Success - ${trackingData.dataType} data (${trackingData.contentLength} bytes, compressed: ${trackingData.isCompressed})`);
                
            } catch (error) {
                if (error.message.includes('encoding') || error.message.includes('character')) {
                    results.characterHandlingStats.encodingIssues++;
                }
                
                results.failed.push({
                    raceNumber: competitor.raceNumber,
                    teamName: competitor.teamName,
                    originalNamesString: competitor.originalNamesString,
                    error: error.message,
                    url: competitor.url,
                    characterHandling: competitor.characterHandling
                });
                
                console.error(`  ✗ Failed: ${error.message}`);
            }
            
            // Add delay between requests
            if (i < this.competitors.length - 1) {
                await this.delay(this.requestDelay);
            }
        }

        results.endTime = new Date().toISOString();
        results.duration = Date.now() - new Date(results.startTime).getTime();
        
        return results;
    }

    async createComprehensiveSummary(results) {
        const summary = `
COMPREHENSIVE TRACKING DATA SUMMARY WITH ENHANCED CHARACTER HANDLING
====================================================================

OVERVIEW
--------
Total Competitors: ${results.totalRequests}
Successful Downloads: ${results.successful.length}
Failed Downloads: ${results.failed.length}
Success Rate: ${((results.successful.length / results.totalRequests) * 100).toFixed(1)}%
Duration: ${(results.duration / 1000).toFixed(1)} seconds

CHARACTER HANDLING STATISTICS
-----------------------------
Competitors with Special Characters: ${results.characterHandlingStats.totalSpecialChars}
Competitors with Apostrophes: ${results.characterHandlingStats.totalApostrophes}
Encoding Issues: ${results.characterHandlingStats.encodingIssues}

DATA ANALYSIS
-------------
Compressed Files: ${results.successful.filter(r => r.isCompressed).length}
Files with Extracted Data: ${results.successful.filter(r => r.hasExtractedData).length}
Total Coordinates Found: ${results.successful.reduce((sum, r) => sum + r.coordinateCount, 0)}
Total Timestamps Found: ${results.successful.reduce((sum, r) => sum + r.timestampCount, 0)}

SUCCESSFUL DOWNLOADS
--------------------
${'Race#'.padEnd(8)} | ${'Team Name'.padEnd(35)} | ${'Special'.padEnd(7)} | ${'Apos'.padEnd(4)} | ${'Type'.padEnd(6)} | ${'Size'.padStart(8)} | ${'Coords'.padStart(6)}
${''.padEnd(8, '-')} | ${''.padEnd(35, '-')} | ${''.padEnd(7, '-')} | ${''.padEnd(4, '-')} | ${''.padEnd(6, '-')} | ${''.padStart(8, '-')} | ${''.padStart(6, '-')}
${results.successful.map(item => 
    `${item.raceNumber.padEnd(8)} | ${item.teamName.substring(0, 35).padEnd(35)} | ${item.hasSpecialChars.toString().padEnd(7)} | ${item.hasApostrophes.toString().padEnd(4)} | ${item.dataType.padEnd(6)} | ${item.dataSize.toString().padStart(8)} | ${item.coordinateCount.toString().padStart(6)}`
).join('\n')}

${results.failed.length > 0 ? `
FAILED DOWNLOADS
----------------
${results.failed.map(item => 
    `${item.raceNumber.padEnd(8)} | ${item.teamName.substring(0, 35).padEnd(35)} | ${item.error}`
).join('\n')}
` : ''}

SPECIAL CHARACTER EXAMPLES
--------------------------
${results.successful.filter(r => r.hasSpecialChars || r.hasApostrophes).slice(0, 10).map(item => 
    `${item.raceNumber}: "${item.originalNamesString}" -> "${item.teamName}"`
).join('\n')}

DIRECTORY STRUCTURE
-------------------
${this.outputDir}/ - Competitor reports (with character handling info)
${this.trackingDir}/ - Complete tracking responses
${this.rawDir}/ - Raw API responses
${this.processedDir}/ - Processed/extracted data (with character mappings)

NOTES
-----
- Enhanced character handling preserves original names while creating safe filenames
- Apostrophes are safely encoded as "_apos_" in filenames
- Unicode normalization ensures consistent character representation
- Character mappings are preserved for potential reversal

Generated on: ${new Date().toISOString()}
`.trim();

        const summaryPath = path.join(this.outputDir, 'comprehensive_summary.txt');
        await fs.promises.writeFile(summaryPath, summary, 'utf8');
        console.log(`\nComprehensive summary written to: ${summaryPath}`);
    }

    async importAllWithTracking(rawData) {
        try {
            console.log('Starting comprehensive competitors tracking import with enhanced character handling...');
            
            // Parse the raw data
            this.parseRawData(rawData);
            console.log(`Parsed ${this.competitors.length} competitors`);
            
            // Fetch all tracking data
            const results = await this.fetchAllTrackingData();
            
            // Create comprehensive summary
            await this.createComprehensiveSummary(results);
            
            console.log('\n🎉 ENHANCED IMPORT COMPLETE 🎉');
            console.log(`Competitor reports: ${this.outputDir}/`);
            console.log(`Tracking data: ${this.trackingDir}/`);
            console.log(`Raw responses: ${this.rawDir}/`);
            console.log(`Processed data: ${this.processedDir}/`);
            console.log(`Success rate: ${((results.successful.length / results.totalRequests) * 100).toFixed(1)}%`);
            console.log(`Special characters handled: ${results.characterHandlingStats.totalSpecialChars}`);
            console.log(`Apostrophes handled: ${results.characterHandlingStats.totalApostrophes}`);
            
            return results;
            
        } catch (error) {
            console.error('Import failed:', error);
            throw error;
        }
    }
}

// Usage
async function main() {
    const rawData = `
-	205 - Sa'aad Variawa Zaheer Bodhanya	01:12:20.0				00:09:27.0	00:23:47.0	00:36:47.0	00:57:21.0	1	12:00:00	13:12:20.0	83.9	01:12:20.0	
00:09:31.0	00:23:51.0	00:36:11.0		2	13:12:20.0		90.4		
-	777 - Wilro Dippenaar Carolyn Swan	01:14:49.0	00:02:29.0			00:10:15.0	00:25:25.0	00:38:45.0	00:59:53.0	1	12:24:00	13:38:49.0	81.1	01:14:49.0	
00:09:57.0				2	13:38:49.0		90.4		
-	296 - Banie Barnard Dawid van Staden	01:14:57.0	00:02:37.0			00:10:34.0	00:25:44.0	00:39:44.0	01:02:32.0	1	12:01:00	13:15:57.0	80.9	01:14:57.0	
00:10:19.0	00:25:09.0	00:38:49.0		2	13:15:57.0		82.2		
-	299 - Lood du Preez Adriaan Roets	01:15:41.0	00:03:21.0			00:10:06.0	00:25:26.0	00:38:46.0	01:00:31.0	1	12:05:00	13:20:41.0	80.2	01:15:41.0	1
00:09:57.0	00:27:17.0	00:37:37.0		2	13:20:41.0		89.4		
-	292 - Keith Smith Juandre Kruger	01:15:50.0	00:03:30.0			00:10:26.0	00:25:56.0	00:39:16.0	01:00:44.0	1	12:06:00	13:21:50.0	80.0	01:15:50.0	6
00:10:07.0	00:25:07.0	00:38:27.0		2	13:21:50.0		91.6		
-	233 - Eugene Bierman Marius Lombard	01:16:07.0	00:03:47.0			00:10:15.0	00:25:45.0	00:39:05.0	01:00:48.0	1	12:09:00	13:25:07.0	79.7	01:16:07.0	
00:10:07.0	00:24:57.0	00:38:07.0		2	13:25:07.0		92.0		
-	370 - Hannes Saaijman Attie Saaijman	01:16:23.0	00:04:03.0			00:10:15.0	00:25:45.0	00:39:19.0	01:01:16.0	1	12:10:00	13:26:23.0	79.4	01:16:23.0	
00:09:56.0	00:27:36.0			2	13:26:23.0		92.4		
-	284 - Loic Bathfield Rodney Burke	01:16:29.0	00:04:09.0			00:10:25.0	00:28:45.0	00:39:25.0	01:01:02.0	1	12:07:00	13:23:29.0	79.3	01:16:29.0	
00:10:27.0	00:25:37.0	00:38:57.0		2	13:23:29.0		89.8		
-	312 - Dewald van Breda Stompie Mynhardt	01:16:46.0	00:04:26.0			00:10:15.0	00:25:35.0	00:39:25.0	01:01:03.0	1	12:04:00	13:20:46.0	79.0	01:16:46.0	2
00:10:18.0	00:25:18.0	00:39:08.0		2	13:20:46.0		89.4		
-	T8 - Ignus Du Plessis Gerhard Du Plessis	01:17:45.0	00:05:25.0			00:10:45.0	00:26:25.0	00:40:15.0	01:02:22.0	1	12:02:00	13:19:45.0	78.0	01:17:45.0	
00:10:56.0	00:26:16.0	00:39:56.0		2	13:19:45.0		87.7		
-	A421 - Gideon Jacobs Junior Vardy	01:18:11.0	00:05:51.0			00:10:36.0	00:29:36.0	00:40:46.0	01:02:41.0	1	12:26:00	13:44:11.0	77.6	01:18:11.0	
00:10:29.0				2	13:44:11.0		82.2		
-	T9 - TG Jansen van Rensburg Vince van Allemann	01:18:45.0	00:06:25.0			00:10:55.0	00:29:55.0	00:40:55.0	01:02:52.0	1	12:08:00	13:26:45.0	77.0	01:18:45.0	
00:11:07.0	00:32:37.0			2	13:26:45.0		71.6		
-	A99 - James Alexander John Kelly	01:19:15.0	00:06:55.0			00:10:25.0	00:29:25.0	00:40:35.0	01:03:18.0	1	12:03:00	13:22:15.0	76.5	01:19:15.0	
00:10:30.0	00:25:50.0	00:39:30.0		2	13:22:15.0		89.1		
-	T7 - Jan Rose Arno Olivier	01:20:51.0	00:08:31.0			00:10:54.0	00:29:04.0	00:43:24.0	01:05:41.0	1	12:11:00	13:31:51.0	75.0	01:20:51.0	
00:10:48.0	00:29:28.0			2	13:31:51.0		86.0		
-	363 - Tokkie Ferreira Mac Van Loggerenberg	01:23:56.0	00:11:36.0			00:11:45.0	00:28:25.0	00:43:36.0	01:07:41.0	1	12:14:00	13:37:56.0	72.3	01:23:56.0	
00:11:48.0				2	13:37:56.0		77.2		
-	343 - Tyler Botha Kyle Lotter	01:24:43.0	00:12:23.0			00:11:26.0	00:31:56.0	00:43:56.0	01:08:29.0	1	12:25:00	13:49:43.0	71.6	01:24:43.0	
00:11:28.0				2	13:49:43.0		71.8		
-	G2 - Leneste Bierman Odette Lombard	01:32:47.0	00:20:27.0			00:12:36.0	00:30:36.0	00:47:56.0	01:14:46.0	1	12:18:00	13:50:47.0	65.4	01:32:47.0	
00:13:07.0				2	13:50:47.0		64.4		
-	D85 - Iaan Olivier Tobie Engelbrecht	01:33:08.0	00:20:48.0			00:13:27.0	00:32:17.0	00:48:57.0	01:15:23.0	1	12:22:00	13:55:08.0	65.1	01:33:08.0	
2	13:55:08.0		63.1		
-	G8 - Walter du Plessis Garth Warncke	01:35:22.0	00:23:02.0			00:14:54.0	00:33:04.0	00:50:14.0	01:18:33.0	1	12:16:00	13:51:22.0	63.6	01:35:22.0	
00:12:19.0				2	13:51:22.0		67.6		
-	B1 - Andre Claassens Annemerie Coetzee	01:39:14.0	00:26:54.0			00:13:56.0	00:33:26.0	00:51:56.0	01:20:37.0	1	12:23:00	14:02:14.0	61.1	01:39:14.0	
2	14:02:14.0		63.3		
-	G1 - Liezl Saaijman Celeste Burger	01:41:31.0	00:29:11.0			00:12:55.0	00:31:55.0	00:49:35.0	01:20:00.0	1	12:13:00	13:54:31.0	59.7	01:41:31.0	
2	13:54:31.0		64.4		
-	D99 - Andre van der Walt Anzel Fick					00:14:06.0				1	12:32:00		21.1		
2					
-	S24 - Wessel Edward Mostert Steff					00:11:36.0				1	12:12:00		16.7		
2					
-	D77 - Alex van Rooyen André Labuschangne									1	12:17:00				
2					
-	G524 - Ian Mostert Aidan Mostert					00:11:24.0	00:29:24.0			1	12:29:00		33.4		
2					
-	A31 - Khalil Hussain Waseem Hussain									1	12:15:00		7.7		
2					
-	D44 - Shaheed Amin Imraan Arbi					00:15:37.0	00:38:37.0	00:58:57.0	01:29:35.0	1	12:27:00		57.8		
2					
-	P77 - Nic Goslar Andrew Massey									1	12:20:00				
2					
-	G99 - Dominique du Preez Neil du Preez					00:12:46.0	00:34:26.0	00:53:26.0	01:23:54.0	1	12:28:00		61.3		
2					
-	CC2 - Philip Mostert Philip Mostert					00:17:48.0	00:40:58.0	01:03:08.0		1	12:34:00		50.1		
2					
-	P7 - Stanley Coetzee Tiaan Coetzee					00:12:34.0	00:31:12.0	01:19:48.0		1	12:21:00		40.8		
2					
-	G9 - Liezel Barnard Jorina van Staden					00:12:15.0	00:29:25.0	01:42:16.0		1	12:19:00		36.3		
    `;

    const importer = new CompetitorsTrackingImporter();
    
    try {
        const results = await importer.importAllWithTracking(rawData);
        console.log('\n🎉 Complete import with enhanced character handling finished successfully!');
        
    } catch (error) {
        console.error('Import failed:', error);
    }
}

// Run the import
if (require.main === module) {
    main();
}

module.exports = CompetitorsTrackingImporter;
