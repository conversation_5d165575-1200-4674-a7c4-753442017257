{"statusCode": 200, "headers": {"server": "nginx", "date": "Mon, 07 Jul 2025 08:50:30 GMT", "content-type": "text/html; charset=utf-8", "transfer-encoding": "chunked", "connection": "keep-alive", "keep-alive": "timeout=75", "vary": "Accept-Encoding", "x-powered-by": "PHP/5.3.3", "set-cookie": ["PHPSESSID=ko3li037i46perf5ktlph1baa3; path=/"], "expires": "Thu, 19 Nov 1981 08:52:00 GMT", "cache-control": "no-store, no-cache, must-revalidate, post-check=0, pre-check=0", "pragma": "no-cache", "strict-transport-security": "max-age=31536000", "content-encoding": "gzip"}, "rawData": "\u001f\b\u0000\u0000\u0000\u0000\u0000\u0000\u0003«VÊ+ÍÍOÊJM.)V²2Ô\u0001qRóR\\\u0003\u001d%¸TtµR^bnªR®gz^i±BJ©B@Njqqf±{jQFbQ\nRJjq2PBpjbq~\u001eP09¿4¯\u0004l8Ü¢èØÚØZ\u0000ÜJ¥\u0000\u0000\u0000", "data": "{\"numobjects\":1,\"numrecords\":0,\"objects\":[{\"name\":\"T8 - Ign<PERSON> <PERSON>less<PERSON>\",\"desc\":\"2024 Season\",\"count\":0,\"records\":[]}]}", "contentType": "text/html; charset=utf-8", "contentLength": 127, "contentEncoding": "gzip", "timestamp": "2025-07-07T08:50:31.022Z", "isCompressed": true, "parsedData": {"numobjects": 1, "numrecords": 0, "objects": [{"name": "T8 - <PERSON><PERSON><PERSON>", "desc": "2024 Season", "count": 0, "records": []}]}, "dataType": "json"}