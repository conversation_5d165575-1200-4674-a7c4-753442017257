<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Event Details - RacedayTV</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&family=Roboto:wght@400;500;700&display=swap" rel="stylesheet">
    <script src="https://unpkg.com/@tailwindcss/browser@4"></script>
    <style>
        body {
            font-family: 'Roboto', sans-serif; /* More like YouTube's font */
        }
        .video-embed iframe {
            aspect-ratio: 16 / 9;
            width: 100%;
        }
        .thumbnail {
            width: 100%;
            height: auto;
            border-radius: 8px;
            object-fit: cover; /* Ensure image fills the container */
        }
        .video-card {
            transition: transform 0.3s ease;
        }
        .video-card:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        }
        .photo-card {
            transition: transform 0.3s ease;
        }
        .photo-card:hover {
            transform: scale(1.03);
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body class="bg-gray-100">
    <header class="bg-white text-gray-800 py-3 flex justify-between items-center shadow-md sticky top-0 z-10 rounded-md">
        <div class="logo text-xl font-bold ml-4">
            <a href="/" class="hover:text-red-600 no-underline flex items-center">
                <span class="text-red-600">Raceday</span><span class="ml-1">TV</span>
            </a>
        </div>
        <nav class="mr-4">
            <ul class="flex space-x-6">
                <li><a href="#event-details" class="hover:text-blue-600 no-underline font-medium">Event Details</a></li>
                <li><a href="#videos" class="hover:text-blue-600 no-underline font-medium">Videos</a></li>
                <li><a href="#photos" class="hover:text-blue-600 no-underline font-medium">Photos</a></li>
                <li><a href="#user-videos" class="hover:text-blue-600 no-underline font-medium">User Videos</a></li>
                <li><a href="#user-photos" class="hover:text-blue-600 no-underline font-medium">Login</a></li>
            </ul>
        </nav>
    </header>

    <section id="event-details" class="bg-gray-100 py-8 px-4 text-left rounded-md">
        <div class="container mx-auto">
            <h2 class="text-3xl font-bold mb-4 text-gray-800">Event Name - <span class="text-red-600">[Event Name]</span></h2>
            <p class="text-sm text-gray-600 mb-2">Date: <span class="font-semibold">[Event Date]</span></p>
            <p class="text-gray-700 leading-relaxed mt-4">
                [Event Description] - Details about the event, location, participating teams, etc.  This section provides key information about the event.
            </p>
        </div>
    </section>

    <section id="videos" class="bg-white py-8 px-4 rounded-md mt-8">
        <div class="container mx-auto">
            <h2 class="text-2xl font-semibold mb-6 text-gray-800">Event Videos</h2>
            <div id="video-gallery" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                </div>
        </div>
    </section>

    <section id="photos" class="bg-gray-100 py-8 px-4 rounded-md mt-8">
        <div class="container mx-auto">
            <h2 class="text-2xl font-semibold mb-6 text-gray-800">Event Photos</h2>
            <div id="photo-gallery" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                </div>
        </div>
    </section>

    <section id="user-videos" class="bg-white py-8 px-4 rounded-md mt-8">
        <div class="container mx-auto">
            <h2 class="text-2xl font-semibold mb-6 text-gray-800">User-Submitted Videos</h2>
            <p class="text-gray-600 mb-4">
                Share your videos from the event via our Telegram bot!
            </p>
            <div id="user-video-gallery" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
            </div>
        </div>
    </section>

    <section id="user-photos" class="bg-gray-100 py-8 px-4 rounded-md mt-8">
        <div class="container mx-auto">
            <h2 class="text-2xl font-semibold mb-6 text-gray-800">User-Submitted Photos</h2>
            <p class="text-gray-600 mb-4">
                Share your photos from the event via our Telegram bot!
            </p>
            <div id="user-photo-gallery" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
            </div>
        </div>
    </section>

    <section id="login" class="bg-gray-100 py-8 px-4 text-center rounded-md mt-8">
        <div class="container mx-auto">
            <h2 class="text-2xl font-semibold mb-6 text-gray-800">Login/Register</h2>
            <p class="text-gray-600 mb-4">
                Login to access exclusive content and share your media. (Note: This is a placeholder - no actual login functionality is implemented in this code)
            </p>
            <div class="flex justify-center gap-4">
                <button onclick="alert('Login functionality not implemented in this demo.')" class="bg-blue-500 hover:bg-blue-700 text-white font-semibold py-2.5 px-5 rounded-full focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-opacity-75">
                    Login with Google
                </button>
                <button onclick="alert('Login functionality not implemented in this demo.')" class="bg-blue-500 hover:bg-blue-700 text-white font-semibold py-2.5 px-5 rounded-full focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-opacity-75">
                    Login with Facebook
                </button>
            </div>
        </div>
    </section>

    <footer class="bg-gray-800 text-white py-6 text-center rounded-md mt-8">
        <div class="container mx-auto">
            <p>© 2024 RacedayTV. All rights reserved.</p>
            <div class="social-links mt-4 flex justify-center gap-6">
                <a href="#" class="hover:text-red-300 no-underline text-lg">Facebook</a>
                <a href="#" class="hover:text-red-300 no-underline text-lg">Twitter</a>
                <a href="#" class="hover:text-red-300 no-underline text-lg">YouTube</a>
            </div>
        </div>
    </footer>

    <script>
        // Sample data for events, videos, photos, and user-submitted content
        const eventName = "Example Race Event";  // Replace with actual event name
        const eventDate = "2024-08-15";        // Replace with actual event date
        const eventDescription = "This is an example event page detailing all the information and media related to the race. Users can find videos, photos, and user-submitted content here.  The page is designed to provide an engaging experience, similar to popular video sharing platforms."; // Replace with actual description

        const videos = [
            { title: "Race Highlights", url: "https://www.youtube.com/embed/VIDEO1_ID", thumbnail: "https://placehold.co/360x200/EEE/31343C?text=Highlights&font=Roboto" }, // Added thumbnail
            { title: "Onboard Camera", url: "https://www.youtube.com/embed/VIDEO2_ID", thumbnail: "https://placehold.co/360x200/EEE/31343C?text=Onboard&font=Roboto" },
            { title: "Post-Race Interviews", url: "https://www.youtube.com/embed/VIDEO3_ID", thumbnail: "https://placehold.co/360x200/EEE/31343C?text=Interviews&font=Roboto" },
            { title: "Crazy Moments", url: "https://www.youtube.com/embed/USER_VIDEO1_ID", thumbnail: "https://placehold.co/360x200/EEE/31343C?text=Moments&font=Roboto" },
        ];

        const photos = [
            { url: "https://placehold.co/600x400/EEE/31343C?text=Race+Photo+1&font=Montserrat", caption: "Start of the race" },
            { url: "https://placehold.co/600x400/EEE/31343C?text=Race+Photo+2&font=Montserrat", caption: "Car Overtake" },
            { url: "https://placehold.co/600x400/EEE/31343C?text=Race+Photo+3&font=Montserrat", caption: "Finish Line" },
        ];

        const userVideos = [
             { title: "User Video 1", url: "https://www.youtube.com/embed/USER_VIDEO1_ID", thumbnail: "https://placehold.co/360x200/CCC/31343C?text=User+Video+1&font=Roboto" },
            { title: "User Video 2", url: "https://www.youtube.com/embed/USER_VIDEO2_ID", thumbnail: "https://placehold.co/360x200/CCC/31343C?text=User+Video+2&font=Roboto" },
        ];

        const userPhotos = [
            { url: "https://placehold.co/600x400/CCC/31343C?text=User+Photo+1&font=Montserrat", caption: "User 1's Photo" },
            { url: "https://placehold.co/600x400/CCC/31343C?text=User+Photo+2&font=Montserrat", caption: "User 2's Photo" },
        ];

        // Function to set event details
        async function setEventDetails() {
            const strapiUrl = "http://cdn1.provisual.media:1349";
            const strapiApiToken = "a7eeafba484951a0d09870949b242fa93024a2b7e8a4909a5b6a0a51b9427e4e784498ddeb98bbccc3350c1bf34ec6e26dc7db1b825f20d7b58ea79f1f125d31df9b8a2bbbcead025924af44e5639fb90399881ba46f4385e013844db4c83943f5cda010b665ff8efc7a715f89f97bbeae4c86d6f63392ab070eceb2fb80eacd";

            try {
                const response = await fetch(`${strapiUrl}/api/rdtv-events`, {
                    headers: {
                        Authorization: `Bearer ${strapiApiToken}`,
                    },
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                console.log("Event Details Data:", data);

                if (data && data.data && data.data.length > 0) {
                    const event = data.data[0]; // Access data directly, not through attributes
                    document.querySelector("#event-details h2 span").textContent = event.name;
                    document.querySelector("#event-details p span").textContent = new Date(event.start).toLocaleDateString();
                    document.querySelector("#event-details p:last-of-type").textContent = event.description || "No description available.";
                } else {
                    console.warn("No event details found in Strapi.");
                    document.querySelector("#event-details h2 span").textContent = "No Event Found";
                    document.querySelector("#event-details p span").textContent = "N/A";
                    document.querySelector("#event-details p:last-of-type").textContent = "No event details available.";
                }
            } catch (error) {
                console.error("Error fetching event details:", error);
                document.querySelector("#event-details h2 span").textContent = "Error Loading Event";
                document.querySelector("#event-details p span").textContent = "N/A";
                document.querySelector("#event-details p:last-of-type").textContent = "Failed to load event details.";
            }
        }

        // Function to generate video gallery
        async function generateVideoGallery() {
            const strapiUrl = "http://cdn1.provisual.media:1349";
            const strapiApiToken = "a7eeafba484951a0d09870949b242fa93024a2b7e8a4909a5b6a0a51b9427e4e784498ddeb98bbccc3350c1bf34ec6e26dc7db1b825f20d7b58ea79f1f125d31df9b8a2bbbcead025924af44e5639fb90399881ba46f4385e013844db4c83943f5cda010b665ff8efc7a715f89f97bbeae4c86d6f63392ab070eceb2fb80eacd";
            const videoGallery = document.getElementById("video-gallery");

            try {
                const response = await fetch(`${strapiUrl}/api/video-contents`, {
                    headers: {
                        Authorization: `Bearer ${strapiApiToken}`,
                    },
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                console.log("Video Contents Data:", data);

                if (data && data.data) {
                    data.data.forEach((video) => {
                        const videoCard = document.createElement("div");
                        videoCard.classList.add("video-card", "rounded-lg", "shadow-md", "overflow-hidden", "bg-white");

                        // Construct the video URL
                        const adjustedTimestamp = parseInt(new Date(video.attributes.utc_start_time).getTime() / 1000) + 7200 - 10;
                        const videoUrl = `${video.attributes.stream_url}${adjustedTimestamp}-${video.attributes.duration_seconds}.m3u8`;

                        videoCard.innerHTML = `
                            <a href="${videoUrl}" class="block">
                                <img src="https://placehold.co/360x200/EEE/31343C?text=Video&font=Roboto" alt="${video.attributes.dvr}" class="thumbnail">
                                <h3 class="text-lg font-semibold mt-2 text-gray-800 p-2">${video.attributes.dvr}</h3>
                            </a>
                        `;
                        videoGallery.appendChild(videoCard);
                    });
                } else {
                    console.warn("No video contents found in Strapi.");
                    videoGallery.innerHTML = `<p class="text-gray-500 p-4">No videos available.</p>`;
                }
            } catch (error) {
                console.error("Error fetching video contents:", error);
                videoGallery.innerHTML = `<p class="text-gray-500 p-4">Error loading videos.</p>`;
            }
        }

        // Function to generate photo gallery
        function generatePhotoGallery() {
            const photoGallery = document.getElementById("photo-gallery");
            photoGallery.innerHTML = ""; // Clear existing content
            for (let i = 1; i <= 6; i++) {
                const photoCard = document.createElement("div");
                photoCard.classList.add("photo-card", "rounded-lg", "shadow-md", "overflow-hidden", "bg-white");
                photoCard.innerHTML = `
                    <img src="https://placehold.co/600x400/EEE/31343C?text=Race+Photo+${i}&font=Montserrat" alt="Race Photo ${i}" class="photo-image w-full h-auto object-cover rounded-t-lg">
                    <p class="text-gray-700 p-4 rounded-b-lg bg-gray-50">Race Photo ${i}</p>
                `;
                photoGallery.appendChild(photoCard);
            }
        }

        // Function to generate user-submitted video gallery
        async function generateUserVideoGallery() {
            const apiUrl = "http://cdn1.provisual.media:1349/api/telegram-contents?populate=*";
            const userVideoGallery = document.getElementById("user-video-gallery");
            const strapiApiToken = "a7eeafba484951a0d09870949b242fa93024a2b7e8a4909a5b6a0a51b9427e4e784498ddeb98bbccc3350c1bf34ec6e26dc7db1b825f20d7b58ea79f1f125d31df9b8a2bbbcead025924af44e5639fb90399881ba46f4385e013844db4c83943f5cda010b665ff8efc7a715f89f97bbeae4c86d6f63392ab070eceb2fb80eacd";
            const headers = {
                Authorization: `Bearer ${strapiApiToken}`,
            };

            try {
                const response = await fetch(apiUrl, { headers });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                console.log("User Video Contents Data:", data);

                if (data && data.data) {
                    userVideoGallery.innerHTML = "";
                    data.data.filter(item => item.media_type === 'video').forEach((video) => {
                        const videoCard = document.createElement("div");
                        videoCard.classList.add("video-card", "rounded-lg", "shadow-md", "overflow-hidden", "bg-white");

                        // Use the video URL from Strapi
                        const mediaUrl = video.media && video.media.length > 0 ? `http://cdn1.provisual.media:1349${video.media[0].url}` : "https://placehold.co/360x200/CCC/31343C?text=User+Video&font=Roboto";

                        videoCard.innerHTML = `
                            <a href="${mediaUrl}" class="block">
                                <img src="https://placehold.co/360x200/CCC/31343C?text=User+Video&font=Roboto" alt="${video.username || 'User Video'}" class="thumbnail">
                                <h3 class="text-lg font-semibold mt-2 text-gray-800 p-2">${video.username || 'Anonymous'}</h3>
                            </a>
                        `;
                        userVideoGallery.appendChild(videoCard);
                    });
                } else {
                    console.warn("No user video contents found in Strapi.");
                    userVideoGallery.innerHTML = `<p class="text-gray-500 p-4">No user videos submitted yet.</p>`;
                }
            } catch (error) {
                console.error("Error fetching user video contents:", error);
                userVideoGallery.innerHTML = `<p class="text-gray-500 p-4">Error loading user videos.</p>`;
            }
        }

        // Function to generate user-submitted photo gallery
        async function generateUserPhotoGallery() {
            const apiUrl = "http://cdn1.provisual.media:1349/api/telegram-contents?populate=*";
            const userPhotoGallery = document.getElementById("user-photo-gallery");
            const strapiApiToken = "a7eeafba484951a0d09870949b242fa93024a2b7e8a4909a5b6a0a51b9427e4e784498ddeb98bbccc3350c1bf34ec6e26dc7db1b825f20d7b58ea79f1f125d31df9b8a2bbbcead025924af44e5639fb90399881ba46f4385e013844db4c83943f5cda010b665ff8efc7a715f89f97bbeae4c86d6f63392ab070eceb2fb80eacd";
            const headers = {
                Authorization: `Bearer ${strapiApiToken}`,
            };

            try {
                const response = await fetch(apiUrl, { headers });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                console.log("User Photo Contents Data:", data);

                if (data && data.data) {
                    userPhotoGallery.innerHTML = ""; // Clear existing content
                    data.data.filter(item => item.media_type === 'photo').forEach((photo) => {
                        const photoCard = document.createElement("div");
                        photoCard.classList.add("photo-card", "rounded-lg", "shadow-md", "overflow-hidden", "bg-white");

                        // Use the photo URL from Strapi
                        const mediaUrl = photo.media && photo.media.length > 0 ? `http://cdn1.provisual.media:1349${photo.media[0].url}` : "https://placehold.co/600x400/CCC/31343C?text=User+Photo&font=Montserrat";

                        photoCard.innerHTML = `
                            <img src="${mediaUrl}" alt="${photo.username || 'User Photo'}" class="photo-image w-full h-auto object-cover rounded-t-lg">
                            <p class="text-gray-700 p-4 rounded-b-lg bg-gray-50">${photo.username || 'Anonymous'}</p>
                        `;
                        userPhotoGallery.appendChild(photoCard);
                    });
                } else {
                    console.warn("No user photo contents found in Strapi.");
                    userPhotoGallery.innerHTML = `<p class="text-gray-500 p-4">No user photos submitted yet.</p>`;
                }
            } catch (error) {
                console.error("Error fetching user photo contents:", error);
                userPhotoGallery.innerHTML = `<p class="text-gray-500 p-4">Error loading user photos.</p>`;
            }
        }

        // Call functions to generate content on page load
        window.onload = () => {
            setEventDetails();
            generateVideoGallery();
            generatePhotoGallery();
            generateUserVideoGallery();
            generateUserPhotoGallery();
        };
    </script>
</body>
</html>
