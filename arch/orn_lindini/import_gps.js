// competitors-tracking-importer.js
const fs = require('fs');
const path = require('path');
const https = require('https');
const http = require('http');

class CompetitorsTrackingImporter {
    constructor() {
        this.competitors = [];
        this.outputDir = './competitors_data';
        this.trackingDir = './tracking_data';
        this.requestDelay = 1000; // 1 second delay between requests
        this.maxRetries = 3;
        this.ensureDirectories();
    }

    ensureDirectories() {
        [this.outputDir, this.trackingDir].forEach(dir => {
            if (!fs.existsSync(dir)) {
                fs.mkdirSync(dir, { recursive: true });
                console.log(`Created directory: ${dir}`);
            }
        });
    }

    parseRawData(rawData) {
        const lines = rawData.trim().split('\n');
        const competitors = [];

        for (const line of lines) {
            if (line.trim().startsWith('-')) {
                const competitor = this.parseSingleCompetitor(line);
                if (competitor) {
                    competitors.push(competitor);
                }
            }
        }

        this.competitors = competitors;
        return competitors;
    }

    parseSingleCompetitor(line) {
        const cleanLine = line.replace(/^-\s*/, '').trim();
        const match = cleanLine.match(/^(\w+)\s*-\s*(.+?)(?=\s+\d{2}:\d{2}:\d{2}\.\d|$)/);
        
        if (!match) return null;

        const raceNumber = match[1];
        const namesString = match[2].trim();
        
        const nameWords = namesString.split(/\s+/);
        let participant1 = '';
        let participant2 = '';
        
        if (nameWords.length >= 2) {
            const midPoint = Math.ceil(nameWords.length / 2);
            participant1 = nameWords.slice(0, midPoint).join(' ');
            participant2 = nameWords.slice(midPoint).join(' ');
        } else {
            participant1 = namesString;
        }

        const times = cleanLine.match(/\d{2}:\d{2}:\d{2}\.\d/g) || [];
        const numericValues = cleanLine.match(/\d+\.\d+/g) || [];
        const integers = cleanLine.match(/\b\d+\b/g) || [];

        const teamName = `${raceNumber} - ${participant1} ${participant2}`.trim();
        const safeFileName = `${raceNumber}_${participant1}_${participant2}`
            .replace(/[^a-zA-Z0-9]/g, '_')
            .replace(/_+/g, '_')
            .replace(/^_|_$/g, '');

        return {
            raceNumber,
            participant1,
            participant2,
            teamName,
            times,
            numericValues,
            integers,
            rawData: cleanLine,
            fileName: `${safeFileName}.txt`,
            trackingFileName: `${safeFileName}_tracking.json`,
            url: this.generateTrackingUrl(teamName)
        };
    }

    generateTrackingUrl(teamName, timestart = 1751698800, timeend = 1751729999) {
        const baseUrl = "https://data.africanstuff.co.za/l/akude_pull.php";
        const encodedName = encodeURIComponent(teamName);
        return `${baseUrl}?objname=${encodedName}&timestart=${timestart}&timeend=${timeend}`;
    }

    async fetchTrackingData(url, retries = 0) {
        return new Promise((resolve, reject) => {
            const urlObj = new URL(url);
            const client = urlObj.protocol === 'https:' ? https : http;
            
            const options = {
                hostname: urlObj.hostname,
                port: urlObj.port,
                path: urlObj.pathname + urlObj.search,
                method: 'GET',
                headers: {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                    'Accept': 'application/json, text/plain, */*',
                    'Accept-Language': 'en-US,en;q=0.9',
                    'Accept-Encoding': 'gzip, deflate',
                    'Connection': 'keep-alive',
                    'Cache-Control': 'no-cache'
                },
                timeout: 30000 // 30 seconds timeout
            };

            const req = client.request(options, (res) => {
                let data = '';

                res.on('data', (chunk) => {
                    data += chunk;
                });

                res.on('end', () => {
                    try {
                        const result = {
                            statusCode: res.statusCode,
                            headers: res.headers,
                            data: data,
                            contentType: res.headers['content-type'] || 'unknown',
                            contentLength: data.length,
                            timestamp: new Date().toISOString()
                        };

                        // Try to parse as JSON if it looks like JSON
                        if (data.trim().startsWith('{') || data.trim().startsWith('[')) {
                            try {
                                result.parsedData = JSON.parse(data);
                                result.dataType = 'json';
                            } catch (e) {
                                result.dataType = 'text';
                                result.parseError = e.message;
                            }
                        } else {
                            result.dataType = 'text';
                        }

                        resolve(result);
                    } catch (error) {
                        reject(error);
                    }
                });
            });

            req.on('error', (error) => {
                if (retries < this.maxRetries) {
                    console.log(`Retrying request (${retries + 1}/${this.maxRetries}): ${url}`);
                    setTimeout(() => {
                        this.fetchTrackingData(url, retries + 1)
                            .then(resolve)
                            .catch(reject);
                    }, (retries + 1) * 2000); // Exponential backoff
                } else {
                    reject(error);
                }
            });

            req.on('timeout', () => {
                req.destroy();
                reject(new Error('Request timeout'));
            });

            req.end();
        });
    }

    async delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    generateCompetitorReport(competitor) {
        const report = `
COMPETITOR REPORT
================

Race Number: ${competitor.raceNumber}
Team Name: ${competitor.teamName}
Participant 1: ${competitor.participant1}
Participant 2: ${competitor.participant2}

TIMING DATA
-----------
${competitor.times.map((time, index) => `Time ${index + 1}: ${time}`).join('\n')}

NUMERIC DATA
------------
${competitor.numericValues.map((val, index) => `Value ${index + 1}: ${val}`).join('\n')}

ADDITIONAL DATA
---------------
${competitor.integers.map((val, index) => `Integer ${index + 1}: ${val}`).join('\n')}

TRACKING DATA
-------------
Tracking file: ${competitor.trackingFileName}
API URL: ${competitor.url}

RAW DATA
--------
${competitor.rawData}

Generated on: ${new Date().toISOString()}
`.trim();

        return report;
    }

    async fetchAllTrackingData() {
        console.log(`Fetching tracking data for ${this.competitors.length} competitors...`);
        
        const results = {
            successful: [],
            failed: [],
            totalRequests: this.competitors.length,
            startTime: new Date().toISOString()
        };

        for (let i = 0; i < this.competitors.length; i++) {
            const competitor = this.competitors[i];
            const progress = `${i + 1}/${this.competitors.length}`;
            
            try {
                console.log(`[${progress}] Fetching: ${competitor.raceNumber} - ${competitor.teamName}`);
                
                // Fetch tracking data
                const trackingData = await this.fetchTrackingData(competitor.url);
                
                // Save tracking data
                const trackingPath = path.join(this.trackingDir, competitor.trackingFileName);
                await fs.promises.writeFile(trackingPath, JSON.stringify(trackingData, null, 2), 'utf8');
                
                // Save competitor report
                const report = this.generateCompetitorReport(competitor);
                const reportPath = path.join(this.outputDir, competitor.fileName);
                await fs.promises.writeFile(reportPath, report, 'utf8');
                
                results.successful.push({
                    raceNumber: competitor.raceNumber,
                    teamName: competitor.teamName,
                    trackingFile: competitor.trackingFileName,
                    reportFile: competitor.fileName,
                    dataSize: trackingData.contentLength,
                    statusCode: trackingData.statusCode,
                    dataType: trackingData.dataType,
                    hasData: trackingData.contentLength > 0
                });
                
                console.log(`  ✓ Success - ${trackingData.dataType} data (${trackingData.contentLength} bytes)`);
                
            } catch (error) {
                results.failed.push({
                    raceNumber: competitor.raceNumber,
                    teamName: competitor.teamName,
                    error: error.message,
                    url: competitor.url
                });
                
                console.error(`  ✗ Failed: ${error.message}`);
            }
            
            // Add delay between requests to be respectful to the server
            if (i < this.competitors.length - 1) {
                await this.delay(this.requestDelay);
            }
        }

        results.endTime = new Date().toISOString();
        results.duration = Date.now() - new Date(results.startTime).getTime();
        
        return results;
    }

    async createTrackingSummary(results) {
        const summary = `
TRACKING DATA FETCH SUMMARY
===========================

Total Competitors: ${results.totalRequests}
Successful Downloads: ${results.successful.length}
Failed Downloads: ${results.failed.length}
Success Rate: ${((results.successful.length / results.totalRequests) * 100).toFixed(1)}%

Duration: ${(results.duration / 1000).toFixed(1)} seconds
Start Time: ${results.startTime}
End Time: ${results.endTime}

SUCCESSFUL DOWNLOADS
--------------------
${results.successful.map(item => 
    `${item.raceNumber.padEnd(6)} | ${item.teamName.padEnd(40)} | ${item.dataType.padEnd(6)} | ${item.dataSize.toString().padStart(8)} bytes | HTTP ${item.statusCode}`
).join('\n')}

DATA TYPE BREAKDOWN
-------------------
${this.getDataTypeBreakdown(results.successful)}

${results.failed.length > 0 ? `
FAILED DOWNLOADS
----------------
${results.failed.map(item => 
    `${item.raceNumber.padEnd(6)} | ${item.teamName.padEnd(40)} | ${item.error}`
).join('\n')}
` : ''}

DIRECTORY STRUCTURE
-------------------
${this.outputDir}/ - Competitor reports
${this.trackingDir}/ - Tracking data (JSON files)

Generated on: ${new Date().toISOString()}
`.trim();

        const summaryPath = path.join(this.outputDir, 'tracking_fetch_summary.txt');
        await fs.promises.writeFile(summaryPath, summary, 'utf8');
        console.log(`\nTracking summary written to: ${summaryPath}`);
    }

    getDataTypeBreakdown(successful) {
        const breakdown = {};
        let totalSize = 0;
        
        successful.forEach(item => {
            breakdown[item.dataType] = breakdown[item.dataType] || { count: 0, size: 0 };
            breakdown[item.dataType].count++;
            breakdown[item.dataType].size += item.dataSize;
            totalSize += item.dataSize;
        });
        
        return Object.entries(breakdown)
            .map(([type, stats]) => 
                `${type.padEnd(8)} | ${stats.count.toString().padStart(5)} files | ${stats.size.toString().padStart(10)} bytes | ${((stats.size / totalSize) * 100).toFixed(1).padStart(6)}%`
            )
            .join('\n') + 
            `\nTotal    | ${successful.length.toString().padStart(5)} files | ${totalSize.toString().padStart(10)} bytes | 100.0%`;
    }

    async createDataAnalysis() {
        console.log('\nAnalyzing tracking data...');
        
        const analysis = {
            totalFiles: 0,
            jsonFiles: 0,
            textFiles: 0,
            emptyFiles: 0,
            errorFiles: 0,
            samples: []
        };

        for (const competitor of this.competitors) {
            const trackingPath = path.join(this.trackingDir, competitor.trackingFileName);
            
            try {
                if (fs.existsSync(trackingPath)) {
                    analysis.totalFiles++;
                    const content = await fs.promises.readFile(trackingPath, 'utf8');
                    const data = JSON.parse(content);
                    
                    if (data.dataType === 'json') {
                        analysis.jsonFiles++;
                    } else if (data.dataType === 'text') {
                        analysis.textFiles++;
                    }
                    
                    if (data.contentLength === 0) {
                        analysis.emptyFiles++;
                    }
                    
                    if (data.statusCode !== 200) {
                        analysis.errorFiles++;
                    }
                    
                    // Sample some data for analysis
                    if (analysis.samples.length < 3 && data.parsedData) {
                        analysis.samples.push({
                            raceNumber: competitor.raceNumber,
                            teamName: competitor.teamName,
                            dataStructure: this.analyzeDataStructure(data.parsedData)
                        });
                    }
                }
            } catch (error) {
                console.error(`Error analyzing ${competitor.trackingFileName}: ${error.message}`);
            }
        }

        const analysisReport = `
TRACKING DATA ANALYSIS
======================

File Statistics:
- Total files: ${analysis.totalFiles}
- JSON format: ${analysis.jsonFiles}
- Text format: ${analysis.textFiles}
- Empty files: ${analysis.emptyFiles}
- Error responses: ${analysis.errorFiles}

Sample Data Structures:
${analysis.samples.map(sample => `
Race ${sample.raceNumber} - ${sample.teamName}:
${sample.dataStructure}
`).join('\n')}

Generated on: ${new Date().toISOString()}
`.trim();

        const analysisPath = path.join(this.outputDir, 'data_analysis.txt');
        await fs.promises.writeFile(analysisPath, analysisReport, 'utf8');
        console.log(`Data analysis written to: ${analysisPath}`);
    }

    analyzeDataStructure(data, prefix = '') {
        if (typeof data !== 'object' || data === null) {
            return `${prefix}${typeof data}: ${JSON.stringify(data).substring(0, 100)}`;
        }
        
        if (Array.isArray(data)) {
            return `${prefix}Array[${data.length}]: ${data.length > 0 ? this.analyzeDataStructure(data[0], prefix + '  ') : 'empty'}`;
        }
        
        const keys = Object.keys(data).slice(0, 5); // First 5 keys
        return keys.map(key => 
            `${prefix}${key}: ${this.analyzeDataStructure(data[key], prefix + '  ')}`
        ).join('\n');
    }

    async importAllWithTracking(rawData) {
        try {
            console.log('Starting comprehensive competitors data import...');
            
            // Parse the raw data
            this.parseRawData(rawData);
            console.log(`Parsed ${this.competitors.length} competitors`);
            
            // Fetch all tracking data
            const results = await this.fetchAllTrackingData();
            
            // Create summary
            await this.createTrackingSummary(results);
            
            // Analyze data
            await this.createDataAnalysis();
            
            console.log('\n=== IMPORT COMPLETE ===');
            console.log(`Competitor files: ${this.outputDir}`);
            console.log(`Tracking files: ${this.trackingDir}`);
            console.log(`Successful: ${results.successful.length}/${results.totalRequests}`);
            console.log(`Failed: ${results.failed.length}/${results.totalRequests}`);
            console.log(`Duration: ${(results.duration / 1000).toFixed(1)} seconds`);
            
            return results;
            
        } catch (error) {
            console.error('Import failed:', error);
            throw error;
        }
    }
}

// Usage
async function main() {
    const rawData = `
-	205 - Sa'aad Variawa Zaheer Bodhanya	01:12:20.0				00:09:27.0	00:23:47.0	00:36:47.0	00:57:21.0	1	12:00:00	13:12:20.0	83.9	01:12:20.0	
00:09:31.0	00:23:51.0	00:36:11.0		2	13:12:20.0		90.4		
-	777 - Wilro Dippenaar Carolyn Swan	01:14:49.0	00:02:29.0			00:10:15.0	00:25:25.0	00:38:45.0	00:59:53.0	1	12:24:00	13:38:49.0	81.1	01:14:49.0	
00:09:57.0				2	13:38:49.0		90.4		
-	296 - Banie Barnard Dawid van Staden	01:14:57.0	00:02:37.0			00:10:34.0	00:25:44.0	00:39:44.0	01:02:32.0	1	12:01:00	13:15:57.0	80.9	01:14:57.0	
00:10:19.0	00:25:09.0	00:38:49.0		2	13:15:57.0		82.2		
-	299 - Lood du Preez Adriaan Roets	01:15:41.0	00:03:21.0			00:10:06.0	00:25:26.0	00:38:46.0	01:00:31.0	1	12:05:00	13:20:41.0	80.2	01:15:41.0	1
00:09:57.0	00:27:17.0	00:37:37.0		2	13:20:41.0		89.4		
-	292 - Keith Smith Juandre Kruger	01:15:50.0	00:03:30.0			00:10:26.0	00:25:56.0	00:39:16.0	01:00:44.0	1	12:06:00	13:21:50.0	80.0	01:15:50.0	6
00:10:07.0	00:25:07.0	00:38:27.0		2	13:21:50.0		91.6		
-	233 - Eugene Bierman Marius Lombard	01:16:07.0	00:03:47.0			00:10:15.0	00:25:45.0	00:39:05.0	01:00:48.0	1	12:09:00	13:25:07.0	79.7	01:16:07.0	
00:10:07.0	00:24:57.0	00:38:07.0		2	13:25:07.0		92.0		
-	370 - Hannes Saaijman Attie Saaijman	01:16:23.0	00:04:03.0			00:10:15.0	00:25:45.0	00:39:19.0	01:01:16.0	1	12:10:00	13:26:23.0	79.4	01:16:23.0	
00:09:56.0	00:27:36.0			2	13:26:23.0		92.4		
-	284 - Loic Bathfield Rodney Burke	01:16:29.0	00:04:09.0			00:10:25.0	00:28:45.0	00:39:25.0	01:01:02.0	1	12:07:00	13:23:29.0	79.3	01:16:29.0	
00:10:27.0	00:25:37.0	00:38:57.0		2	13:23:29.0		89.8		
-	312 - Dewald van Breda Stompie Mynhardt	01:16:46.0	00:04:26.0			00:10:15.0	00:25:35.0	00:39:25.0	01:01:03.0	1	12:04:00	13:20:46.0	79.0	01:16:46.0	2
00:10:18.0	00:25:18.0	00:39:08.0		2	13:20:46.0		89.4		
-	T8 - Ignus Du Plessis Gerhard Du Plessis	01:17:45.0	00:05:25.0			00:10:45.0	00:26:25.0	00:40:15.0	01:02:22.0	1	12:02:00	13:19:45.0	78.0	01:17:45.0	
00:10:56.0	00:26:16.0	00:39:56.0		2	13:19:45.0		87.7		
-	A421 - Gideon Jacobs Junior Vardy	01:18:11.0	00:05:51.0			00:10:36.0	00:29:36.0	00:40:46.0	01:02:41.0	1	12:26:00	13:44:11.0	77.6	01:18:11.0	
00:10:29.0				2	13:44:11.0		82.2		
-	T9 - TG Jansen van Rensburg Vince van Allemann	01:18:45.0	00:06:25.0			00:10:55.0	00:29:55.0	00:40:55.0	01:02:52.0	1	12:08:00	13:26:45.0	77.0	01:18:45.0	
00:11:07.0	00:32:37.0			2	13:26:45.0		71.6		
-	A99 - James Alexander John Kelly	01:19:15.0	00:06:55.0			00:10:25.0	00:29:25.0	00:40:35.0	01:03:18.0	1	12:03:00	13:22:15.0	76.5	01:19:15.0	
00:10:30.0	00:25:50.0	00:39:30.0		2	13:22:15.0		89.1		
-	T7 - Jan Rose Arno Olivier	01:20:51.0	00:08:31.0			00:10:54.0	00:29:04.0	00:43:24.0	01:05:41.0	1	12:11:00	13:31:51.0	75.0	01:20:51.0	
00:10:48.0	00:29:28.0			2	13:31:51.0		86.0		
-	363 - Tokkie Ferreira Mac Van Loggerenberg	01:23:56.0	00:11:36.0			00:11:45.0	00:28:25.0	00:43:36.0	01:07:41.0	1	12:14:00	13:37:56.0	72.3	01:23:56.0	
00:11:48.0				2	13:37:56.0		77.2		
-	343 - Tyler Botha Kyle Lotter	01:24:43.0	00:12:23.0			00:11:26.0	00:31:56.0	00:43:56.0	01:08:29.0	1	12:25:00	13:49:43.0	71.6	01:24:43.0	
00:11:28.0				2	13:49:43.0		71.8		
-	G2 - Leneste Bierman Odette Lombard	01:32:47.0	00:20:27.0			00:12:36.0	00:30:36.0	00:47:56.0	01:14:46.0	1	12:18:00	13:50:47.0	65.4	01:32:47.0	
00:13:07.0				2	13:50:47.0		64.4		
-	D85 - Iaan Olivier Tobie Engelbrecht	01:33:08.0	00:20:48.0			00:13:27.0	00:32:17.0	00:48:57.0	01:15:23.0	1	12:22:00	13:55:08.0	65.1	01:33:08.0	
2	13:55:08.0		63.1		
-	G8 - Walter du Plessis Garth Warncke	01:35:22.0	00:23:02.0			00:14:54.0	00:33:04.0	00:50:14.0	01:18:33.0	1	12:16:00	13:51:22.0	63.6	01:35:22.0	
00:12:19.0				2	13:51:22.0		67.6		
-	B1 - Andre Claassens Annemerie Coetzee	01:39:14.0	00:26:54.0			00:13:56.0	00:33:26.0	00:51:56.0	01:20:37.0	1	12:23:00	14:02:14.0	61.1	01:39:14.0	
2	14:02:14.0		63.3		
-	G1 - Liezl Saaijman Celeste Burger	01:41:31.0	00:29:11.0			00:12:55.0	00:31:55.0	00:49:35.0	01:20:00.0	1	12:13:00	13:54:31.0	59.7	01:41:31.0	
2	13:54:31.0		64.4		
-	D99 - Andre van der Walt Anzel Fick					00:14:06.0				1	12:32:00		21.1		
2					
-	S24 - Wessel Edward Mostert Steff					00:11:36.0				1	12:12:00		16.7		
2					
-	D77 - Alex van Rooyen André Labuschangne									1	12:17:00				
2					
-	G524 - Ian Mostert Aidan Mostert					00:11:24.0	00:29:24.0			1	12:29:00		33.4		
2					
-	A31 - Khalil Hussain Waseem Hussain									1	12:15:00		7.7		
2					
-	D44 - Shaheed Amin Imraan Arbi					00:15:37.0	00:38:37.0	00:58:57.0	01:29:35.0	1	12:27:00		57.8		
2					
-	P77 - Nic Goslar Andrew Massey									1	12:20:00				
2					
-	G99 - Dominique du Preez Neil du Preez					00:12:46.0	00:34:26.0	00:53:26.0	01:23:54.0	1	12:28:00		61.3		
2					
-	CC2 - Philip Mostert Philip Mostert					00:17:48.0	00:40:58.0	01:03:08.0		1	12:34:00		50.1		
2					
-	P7 - Stanley Coetzee Tiaan Coetzee					00:12:34.0	00:31:12.0	01:19:48.0		1	12:21:00		40.8		
2					
-	G9 - Liezel Barnard Jorina van Staden					00:12:15.0	00:29:25.0	01:42:16.0		1	12:19:00		36.3		
    `;

    const importer = new CompetitorsTrackingImporter();
    
    try {
        const results = await importer.importAllWithTracking(rawData);
        console.log('\n🎉 Complete import finished successfully!');
        
    } catch (error) {
        console.error('Import failed:', error);
    }
}

// Run the import
if (require.main === module) {
    main();
}

module.exports = CompetitorsTrackingImporter;