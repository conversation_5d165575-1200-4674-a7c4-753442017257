document.addEventListener('DOMContentLoaded', function() {
    const timeline = document.getElementById('timeline');
    const strapiUrl = "http://cdn1.provisual.media:1349";
    const strapiApiToken = "a7eeafba484951a0d09870949b242fa93024a2b7e8a4909a5b6a0a51b9427e4e784498ddeb98bbccc3350c1bf34ec6e26dc7db1b825f20d7b58ea79f1f125d31df9b8a2bbbcead025924af44e5639fb90399881ba46f4385e013844db4c83943f5cda010b665ff8efc7a715f89f97bbeae4c86d6f63392ab070eceb2fb80eacd";

    let currentStart = 1; // Start at page 1
    const postsPerPage = 10; // Number of posts to fetch per page

    async function fetchTimelineData(page = currentStart, pageSize = postsPerPage) {
        try {
            const response = await fetch(`${strapiUrl}/api/telegram-contents?populate=*&pagination[page]=${page}&pagination[pageSize]=${pageSize}`, {
                headers: {
                    Authorization: `Bearer ${strapiApiToken}`,
                },
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            console.log("Timeline Data:", data);

            if (data && data.data) {
                renderTimelinePosts(data.data);
                currentStart++; // Increment page number
            } else {
                timeline.innerHTML = '<p>No posts available.</p>';
            }
        } catch (error) {
            console.error("Error fetching timeline data:", error);
            timeline.innerHTML = '<p>Error loading posts.</p>';
        }
    }

    function renderTimelinePosts(posts) {
        // timeline.innerHTML = ''; // Clear existing content

        posts.forEach(post => {
            const postElement = document.createElement('div');
            postElement.classList.add('post');

            let mediaElement;
            if (post.media_type === 'video') {
                const mediaUrl = post.media && post.media.length > 0 ? `http://cdn1.provisual.media:1349${post.media[0].url}` : "https://placehold.co/600x400/CCC/31343C?text=User+Video&font=Montserrat";
                mediaElement = `<video src="${mediaUrl}" controls width="100%"></video>`;
            } else if (post.media_type === 'photo') {
                const mediaUrl = post.media && post.media.length > 0 ? `http://cdn1.provisual.media:1349${post.media[0].url}` : "https://placehold.co/600x400/CCC/31343C?text=User+Photo&font=Montserrat";
                mediaElement = `<img src="${mediaUrl}" alt="Post Image" width="100%">`;
            } else {
                mediaElement = '<p>Unsupported media type</p>';
            }

            postElement.innerHTML = `
                ${mediaElement}
                <div class="overlay">
                    <h3>${post.username || 'Anonymous'}</h3>
                    <p>${post.original_filename || ''}</p>
                    <div class="post-actions">
                        <button>Like</button>
                        <button>Comment</button>
                    </div>
                </div>
            `;
            timeline.appendChild(postElement);
        });
    }

    fetchTimelineData();

    timeline.addEventListener('scroll', () => {
        if (timeline.scrollTop + timeline.clientHeight >= timeline.scrollHeight) {
            console.log("Fetching more data...");
            fetchTimelineData();
        }
    });
});
