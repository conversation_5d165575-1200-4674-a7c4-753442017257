body, h1, h2, p, ul, li {
    margin: 0;
    padding: 0;
    border: 0;
}

body {
    font-family: sans-serif;
    background-color: #000; /* Black background */
    color: #fff; /* White text */
}

#timeline {
    min-height: 100vh; /* Ensure it fills at least the screen height */
    overflow-y: scroll; /* Enable vertical scrolling */
    position: relative; /* For absolute positioning of posts */
}

.post {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.post video {
    object-fit: cover; /* Fill the screen, cropping if necessary */
    width: 100%;
    height: 100%;
}

.overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 1rem;
    color: white;
    background: rgba(0, 0, 0, 0.5); /* Semi-transparent background */
}

.post-actions {
    /* Style the like/comment buttons */
}
