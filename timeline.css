/* ===== CSS Variables ===== */
:root {
    /* Colors */
    --primary: #e44d26;
    --primary-light: #ff6b4a;
    --primary-dark: #c0351a;
    --secondary: #1a73e8;
    --dark: #1c1e21;
    --dark-gray: #65676b;
    --medium-gray: #a0a3a8;
    --light-gray: #e4e6eb;
    --extra-light-gray: #f0f2f5;
    --white: #ffffff;
    --black: #000000;
    --success: #34a853;
    --warning: #f9ab00;
    --danger: #ea4335;
  
    /* Shadows */
    --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.12);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
    --shadow-inset: inset 0 1px 3px rgba(0, 0, 0, 0.1);
  
    /* Border Radius */
    --radius-sm: 4px;
    --radius-md: 8px;
    --radius-lg: 12px;
    --radius-xl: 16px;
    --radius-circle: 50%;
  
    /* Transitions */
    --transition-fast: 0.15s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.45s ease;
  
    /* Spacing */
    --space-xs: 0.25rem;
    --space-sm: 0.5rem;
    --space-md: 1rem;
    --space-lg: 1.5rem;
    --space-xl: 2rem;
  }
  
  /* ===== Base Styles ===== */
  * {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
  }
  
  body {
    font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 
                 Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    background-color: var(--extra-light-gray);
    color: var(--dark);
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
  
  /* ===== Layout Structure ===== */
  .app-container {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
  }
  
  .app-header {
    background-color: var(--white);
    box-shadow: var(--shadow-sm);
    position: sticky;
    top: 0;
    z-index: 100;
    padding: var(--space-sm) 0;
  }
  
  .header-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--space-md);
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .logo-container {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
  }
  
  .logo-icon {
    color: var(--primary);
    font-size: 1.5rem;
  }
  
  .app-header h1 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary);
    margin: 0;
  }
  
  .header-nav {
    display: flex;
    gap: var(--space-md);
  }
  
  .timeline-container {
    flex: 1;
    max-width: 1200px;
    margin: 0 auto;
    width: 100%;
    padding: var(--space-md);
  }
  
  .app-footer {
    background-color: var(--white);
    box-shadow: 0 -1px 3px rgba(0, 0, 0, 0.05);
    padding: var(--space-lg) 0;
    margin-top: auto;
  }
  
  /* ===== Stories Section ===== */
  .stories-container {
    display: flex;
    gap: var(--space-md);
    padding: var(--space-md) 0;
    margin-bottom: var(--space-md);
    overflow-x: auto;
    scrollbar-width: none;
  }
  
  .stories-container::-webkit-scrollbar {
    display: none;
  }
  
  .story {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--space-sm);
    min-width: 80px;
    cursor: pointer;
  }
  
  .story-avatar {
    width: 70px;
    height: 70px;
    border-radius: var(--radius-circle);
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(45deg, #f09433, #e6683c, #dc2743, #cc2366, #bc1888);
    padding: 2px;
  }
  
  .story-avatar::after {
    content: '';
    display: block;
    width: 100%;
    height: 100%;
    background-color: var(--white);
    border-radius: var(--radius-circle);
    background-position: center;
    background-size: cover;
    background-repeat: no-repeat;
  }
  
  .new-story {
    background: var(--light-gray);
  }
  
  .new-story i {
    color: var(--dark);
    font-size: 1.25rem;
  }
  
  .story span {
    font-size: 0.75rem;
    color: var(--dark);
    text-align: center;
    max-width: 70px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  
  /* ===== Timeline Posts ===== */
  #timeline {
    display: flex;
    flex-direction: column;
    gap: var(--space-lg);
  }
  
  .post {
    background-color: var(--white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
    transition: transform var(--transition-normal), box-shadow var(--transition-normal);
  }
  
  .post:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-md);
  }
  
  .post-header {
    display: flex;
    align-items: center;
    padding: var(--space-sm) var(--space-md);
    gap: var(--space-sm);
  }
  
  .post-avatar {
    width: 40px;
    height: 40px;
    border-radius: var(--radius-circle);
    background-color: var(--extra-light-gray);
    overflow: hidden;
  }
  
  .post-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  .post-user {
    flex: 1;
  }
  
  .post-username {
    font-weight: 600;
    font-size: 0.95rem;
    color: var(--dark);
  }
  
  .post-time {
    font-size: 0.75rem;
    color: var(--dark-gray);
  }
  
  .post-more {
    color: var(--dark-gray);
    font-size: 1.25rem;
    cursor: pointer;
    padding: var(--space-xs);
  }
  
  /* ===== Modern Video Player ===== */
  .video-player-container {
    position: relative;
    width: 100%;
    background-color: var(--black);
    border-radius: var(--radius-md);
    overflow: hidden;
    margin-bottom: var(--space-sm);
  }
  
  /* Portrait video styles */
.video-player-container.portrait {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 2000; /* Standardized z-index */
    border-radius: 0;
    margin-bottom: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: rgba(0, 0, 0, 0.8); /* Semi-transparent background */
    pointer-events: auto; /* Ensure clicks are captured */
}

/* Add scroll lock to body when portrait video is open */
body.portrait-video-open {
    overflow: hidden;
    /* Prevent the body from turning black */
    background-color: var(--extra-light-gray);
}
  
.video-player-container.portrait .video-wrapper {
    padding-top: 0; /* Remove conflicting padding */
    position: relative;
    height: auto;
    max-height: 90vh;
    width: auto; /* Let width be determined by aspect ratio */
    max-width: 90vw;
    background-color: transparent;
}
  
  .video-player-container.portrait .modern-video-player {
    position: relative;
    object-fit: contain;
    width: auto;
    height: auto;
    max-height: 90vh;
    max-width: 90vw;
}
  
.video-player-container.portrait .video-controls {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    width: 100%;
    background: rgba(0, 0, 0, 0.7);
    padding-top: env(safe-area-inset-top);
    padding-bottom: env(safe-area-inset-bottom);
    z-index: 2001; /* Higher than container to ensure visibility */
}
  
  .video-player-container.portrait .fullscreen {
    display: none;
  }
  
  /* Portrait close button */
  .portrait-close-btn {
    position: absolute;
    top: calc(20px + env(safe-area-inset-top)); /* Adjust for notch/status bar */
    left: calc(20px + env(safe-area-inset-left)); /* Adjust for edge protection */
    background: rgba(0, 0, 0, 0.5);
    color: white;
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 1001;
    cursor: pointer;
    font-size: 1.2rem;
  }
  
  .video-player-container.portrait-active .portrait-close-btn {
    display: flex;
  }
  
  .video-wrapper {
    position: relative;
    padding-top: 56.25%; /* 16:9 Aspect Ratio */
  }
  
  .modern-video-player {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: var(--black);
    outline: none;
  }
  
  .video-controls {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(to top, rgba(0,0,0,0.7), transparent);
    padding: var(--space-sm) var(--space-md);
    display: flex;
    flex-direction: column;
    gap: var(--space-sm);
    opacity: 0;
    transition: opacity var(--transition-fast);
    z-index: 10;
  }
  
  .video-player-container:hover .video-controls,
  .video-controls.visible {
    opacity: 1;
  }
  
  .progress-container {
    width: 100%;
    height: 5px;
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 2.5px;
    position: relative;
    cursor: pointer;
  }
  
  .progress-bar, .buffered-bar {
    position: absolute;
    height: 100%;
    border-radius: 2.5px;
  }
  
  .buffered-bar {
    background-color: rgba(255, 255, 255, 0.4);
    width: 0;
  }
  
  .progress-bar {
    background-color: var(--primary);
    width: 0;
    z-index: 2;
  }
  
  .time-rail {
    display: flex;
    justify-content: space-between;
    margin-top: var(--space-xs);
    font-size: 0.75rem;
    color: rgba(255, 255, 255, 0.8);
  }
  
  .controls-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
  }
  
  .left-controls, .right-controls {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
  }
  
  .control-button {
    background: transparent;
    border: none;
    color: var(--white);
    font-size: 1rem;
    cursor: pointer;
    padding: var(--space-xs);
    border-radius: var(--radius-circle);
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color var(--transition-fast);
  }
  
  .control-button:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }
  
  .volume-slider-container {
    width: 0;
    overflow: hidden;
    transition: width var(--transition-fast);
    display: flex;
    align-items: center;
  }
  
  .volume:hover .volume-slider-container {
    width: 80px;
  }
  
  .volume-slider {
    -webkit-appearance: none;
    width: 100%;
    height: 4px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 2px;
    outline: none;
    margin: 0 var(--space-sm);
  }
  
  .volume-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 12px;
    height: 12px;
    border-radius: var(--radius-circle);
    background: var(--primary);
    cursor: pointer;
  }
  
  .time-display {
    font-size: 0.85rem;
    color: rgba(255, 255, 255, 0.9);
    font-family: monospace;
  }
  
  .playback-speed, .portrait-mode-toggle {
    font-size: 0.85rem;
    color: var(--white);
    background: rgba(255, 255, 255, 0.1);
    padding: var(--space-xs) var(--space-sm);
    border-radius: var(--radius-sm);
  }
  
  /* Additional style for the portrait mode toggle */
  .portrait-mode-toggle {
    margin-right: var(--space-xs);
  }
  
  /* ===== Post Content ===== */
  .post-image {
    width: 100%;
    max-height: 600px;
    object-fit: cover;
    display: block;
  }
  
  .post-actions {
    display: flex;
    justify-content: space-between;
    padding: var(--space-sm) var(--space-md);
    border-top: 1px solid var(--light-gray);
    border-bottom: 1px solid var(--light-gray);
  }
  
  .action-group {
    display: flex;
    gap: var(--space-md);
  }
  
  .action-button {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--dark);
    cursor: pointer;
    padding: var(--space-xs);
    transition: color var(--transition-fast);
  }
  
  .action-button:hover {
    color: var(--primary);
  }
  
  .action-button.liked {
    color: var(--danger);
  }
  
  .post-likes {
    padding: var(--space-sm) var(--space-md);
    font-weight: 600;
    font-size: 0.9rem;
  }
  
  .post-caption {
    padding: 0 var(--space-md) var(--space-sm);
    font-size: 0.95rem;
  }
  
  .post-comments {
    padding: 0 var(--space-md) var(--space-sm);
    font-size: 0.9rem;
    color: var(--dark-gray);
  }
  
  .view-all {
    color: var(--dark-gray);
    cursor: pointer;
    margin-top: var(--space-xs);
    display: block;
  }
  
  .comment-form-container {
    padding: 0 var(--space-md) var(--space-md);
  }
  
  .comment-form {
    display: flex;
    gap: var(--space-sm);
  }
  
  .comment-input {
    flex: 1;
    border: 1px solid var(--light-gray);
    border-radius: var(--radius-xl);
    padding: var(--space-sm) var(--space-md);
    font-family: inherit;
    transition: border-color var(--transition-fast);
  }
  
  .comment-input:focus {
    outline: none;
    border-color: var(--primary);
  }
  
  .comment-submit {
    background: none;
    border: none;
    color: var(--primary);
    font-weight: 600;
    cursor: pointer;
    opacity: 0.7;
    transition: opacity var(--transition-fast);
  }
  
  .comment-submit:hover {
    opacity: 1;
  }
  
  .comment-submit:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
  
  /* ===== Loading States ===== */
  .initial-loader {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--space-xl);
    gap: var(--space-md);
  }
  
  .loader-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid var(--light-gray);
    border-top: 4px solid var(--primary);
    border-radius: var(--radius-circle);
    animation: spin 1s linear infinite;
  }
  
  /* ===== Animations ===== */
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
  
  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }
  
  /* ===== Responsive Design ===== */
  @media (max-width: 768px) {
    .header-content {
      flex-direction: column;
      gap: var(--space-sm);
      padding: var(--space-sm);
    }
    
    .header-nav {
      width: 100%;
      justify-content: space-around;
    }
    
    .nav-button {
      padding: var(--space-sm);
      flex: 1;
      justify-content: center;
    }
    
    .nav-button span {
      display: none;
    }
    
    .timeline-container {
      padding: 0;
    }
    
    .post {
      border-radius: 0;
      box-shadow: none;
      border-bottom: 1px solid var(--light-gray);
    }
    
    .post:hover {
      transform: none;
      box-shadow: none;
    }
    
    /* Adjust portrait video for mobile - z-index already standardized */
    
    .portrait-close-btn {
      top: calc(10px + env(safe-area-inset-top));
      left: calc(10px + env(safe-area-inset-left));
      width: 36px;
      height: 36px;
    }
  }
  
  @media (max-width: 480px) {
    .stories-container {
      gap: var(--space-sm);
      padding: var(--space-sm);
    }
    
    .story {
      min-width: 70px;
    }
    
    .story-avatar {
      width: 60px;
      height: 60px;
    }
    
    .action-button {
      font-size: 1.25rem;
    }
    
    .video-controls {
      padding: var(--space-xs) var(--space-sm);
    }
    
    .control-button {
      width: 28px;
      height: 28px;
      font-size: 0.9rem;
    }
    
    .time-display {
      font-size: 0.75rem;
    }
    
    /* Adjust portrait video controls for small devices */
    .video-player-container.portrait .video-controls {
      padding: var(--space-xs) var(--space-sm) calc(var(--space-xs) + env(safe-area-inset-bottom));
    }
  }
